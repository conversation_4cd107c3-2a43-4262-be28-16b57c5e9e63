import useSWR from 'swr';
import { PageDto } from '@/app/dto/page.dto';
import { API_ENDPOINTS } from '@/lib/api/api-endpoints';
import { fetcher } from '@/lib/api/fetcher';

interface UsePageOptions {
  slug: string;
  initialData?: PageDto;
}

interface UsePageResult {
  page?: PageDto;
  isLoading: boolean;
  isError: boolean;
  error?: any;
}

export const usePage = ({ slug, initialData }: UsePageOptions): UsePageResult => {
  const { data, error, isLoading } = useSWR<PageDto>(
    slug ? `${API_ENDPOINTS.PAGES}/${slug}` : null,
    fetcher,
    { initialData }
  );

  return {
    page: data,
    isLoading,
    isError: !!error,
    error,
  };
};
