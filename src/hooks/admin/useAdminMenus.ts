import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface MenuItem {
  id: string;
  title: string;
  url?: string;
  type?: "LINK" | "PAGE" | "CATEGORY" | "PRODUCT" | "CUSTOM" | "SEPARATOR";
  target?: string;
  icon?: string;
  cssClass?: string;
  parentId?: string | null;
  order: number;
  isActive: boolean;
  children?: MenuItem[];
}

export interface MenuItemFormData {
  title: string;
  url?: string;
  type?: "LINK" | "PAGE" | "CATEGORY" | "PRODUCT" | "CUSTOM" | "SEPARATOR";
  target?: string;
  icon?: string;
  cssClass?: string;
  parentId?: string | null;
  order?: number;
  isActive?: boolean;
}

export interface UseAdminMenusOptions {
  onCreateSuccess?: (item: MenuItem) => void;
  onUpdateSuccess?: (item: MenuItem) => void;
  onDeleteSuccess?: (itemId: string) => void;
}

export function useAdminMenus(menuId: string, options: UseAdminMenusOptions = {}) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState<string | null>(null);

  const fetchMenuItems = useCallback(async () => {
    if (!menuId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/menus/${menuId}/items`);
      const data = await response.json();

      if (response.ok) {
        setMenuItems(data.items || []);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải menu items");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải menu items";
      toast.error(errorMessage);
      console.error("Fetch menu items error:", error);
    } finally {
      setLoading(false);
    }
  }, [menuId]);

  const createMenuItem = async (itemData: MenuItemFormData) => {
    setCreating(true);
    try {
      const response = await fetch("/api/admin/menu-items", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...itemData,
          menuId,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi tạo menu item");
      }

      toast.success("Tạo menu item thành công!");
      
      if (options.onCreateSuccess && result.item) {
        options.onCreateSuccess(result.item);
      }

      // Refresh menu items
      await fetchMenuItems();

      return { success: true, item: result.item };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tạo menu item";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setCreating(false);
    }
  };

  const updateMenuItem = async (itemId: string, itemData: MenuItemFormData) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/admin/menu-items/${itemId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(itemData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi cập nhật menu item");
      }

      toast.success("Cập nhật menu item thành công!");
      
      if (options.onUpdateSuccess && result.item) {
        options.onUpdateSuccess(result.item);
      }

      // Refresh menu items
      await fetchMenuItems();

      return { success: true, item: result.item };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi cập nhật menu item";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setUpdating(false);
    }
  };

  const deleteMenuItem = async (itemId: string) => {
    setDeleting(itemId);
    try {
      const response = await fetch(`/api/admin/menu-items/${itemId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi xóa menu item");
      }

      toast.success("Xóa menu item thành công!");
      
      if (options.onDeleteSuccess) {
        options.onDeleteSuccess(itemId);
      }

      // Refresh menu items
      await fetchMenuItems();

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi xóa menu item";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setDeleting(null);
    }
  };

  useEffect(() => {
    fetchMenuItems();
  }, [fetchMenuItems]);

  return {
    menuItems,
    loading,
    creating,
    updating,
    deleting,
    fetchMenuItems,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
  };
}