import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface GlobalSEOSettings {
  id: string;
  siteName?: string;
  siteDescription?: string;
  siteKeywords: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: any;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
  createdAt: string;
  updatedAt: string;
}

export const useAdminSEO = () => {
  const [globalSEO, setGlobalSEO] = useState<GlobalSEOSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const loadGlobalSEO = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/seo/global");
      if (response.ok) {
        const data = await response.json();
        setGlobalSEO(data);
      } else {
        toast.error("Có lỗi xảy ra khi tải cài đặt SEO");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải cài đặt SEO");
    } finally {
      setLoading(false);
    }
  }, []);

  const saveGlobalSEO = useCallback(async (seoData: GlobalSEOSettings) => {
    setSaving(true);
    try {
      const response = await fetch("/api/admin/seo/global", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(seoData),
      });

      if (response.ok) {
        const data = await response.json();
        setGlobalSEO(data);
        toast.success("Cài đặt SEO đã được lưu thành công");
        return { success: true, data };
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi lưu cài đặt SEO");
        return { success: false, error: error.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi lưu cài đặt SEO");
      return { success: false, error: "Network error" };
    } finally {
      setSaving(false);
    }
  }, []);

  const updateGlobalSEO = useCallback((updates: Partial<GlobalSEOSettings>) => {
    setGlobalSEO((prev) => prev ? { ...prev, ...updates } : null);
  }, []);

  return {
    globalSEO,
    loading,
    saving,
    loadGlobalSEO,
    saveGlobalSEO,
    updateGlobalSEO,
  };
};