import { useCallback, useEffect, useRef } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";

export interface NotificationDto {
  id: string;
  title: string;
  message: string;
  type: "INFO" | "SUCCESS" | "WARNING" | "ERROR" | "SYSTEM";
  priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  targetType: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_ADMIN" | "ROLE_MODERATOR";
  targetId?: string;
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
  target?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface NotificationPreferencesDto {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  categories: {
    orders: boolean;
    products: boolean;
    users: boolean;
    system: boolean;
  };
}

export interface AdminNotificationsFilters {
  search?: string;
  type?: "INFO" | "SUCCESS" | "WARNING" | "ERROR" | "SYSTEM";
  priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_ADMIN" | "ROLE_MODERATOR";
  isRead?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export function useAdminNotifications() {
  const {
    data: notifications,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-notifications");

  const { apiCall, isOperationLoading } = useAdminApi();

  // Use refs to store stable references for dependencies
  const updateDataRef = useRef(updateData);
  const updatePaginationRef = useRef(updatePagination);
  const updateFiltersRef = useRef(updateFilters);
  const setLoadingRef = useRef(setLoading);
  const setErrorRef = useRef(setError);
  const notificationsRef = useRef(notifications);
  const paginationRef = useRef(pagination);
  const filtersRef = useRef(filters);

  // Update refs when values change
  updateDataRef.current = updateData;
  updatePaginationRef.current = updatePagination;
  updateFiltersRef.current = updateFilters;
  setLoadingRef.current = setLoading;
  setErrorRef.current = setError;
  notificationsRef.current = notifications;
  paginationRef.current = pagination;
  filtersRef.current = filters;

  const fetchNotifications = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminNotificationsFilters = {}
    ) => {
      setLoadingRef.current(true);
      setErrorRef.current(null);

      try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        
        // Add filters with proper string conversion
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '' && value !== null) {
            params.append(key, value.toString());
          }
        });

        const response = await apiCall<NotificationDto[]>(
          `/api/admin/notifications?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateDataRef.current(response.data);
          if (response.pagination) {
            updatePaginationRef.current(response.pagination);
          }
          updateFiltersRef.current(searchFilters);
        }
      } catch (err) {
        setErrorRef.current(
          err instanceof Error ? err.message : "Failed to fetch notifications"
        );
      }
    },
    [apiCall] // Only depend on apiCall
  );

  const getNotificationById = useCallback(
    async (id: string): Promise<NotificationDto | null> => {
      try {
        const response = await apiCall<NotificationDto>(
          `/api/admin/notifications/${id}`,
          { method: "GET" },
          `get-notification-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get notification"
        );
      }
    },
    [apiCall]
  );

  const markAsRead = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "read" },
          },
          `mark-read-${id}`
        );

        if (response.success) {
          const updatedNotifications = notificationsRef.current.map((notif) =>
            notif.id === id
              ? {
                  ...notif,
                  status: "read" as const,
                  readAt: new Date().toISOString(),
                }
              : notif
          );
          updateDataRef.current(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to mark as read"
        );
      }
    },
    [apiCall] // Only depend on apiCall
  );

  const markAsUnread = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "UNREAD" },
          },
          `mark-unread-${id}`
        );

        if (response.success) {
          const updatedNotifications = notificationsRef.current.map((notif) =>
            notif.id === id
              ? { ...notif, status: "UNREAD" as const, readAt: undefined }
              : notif
          );
          updateDataRef.current(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to mark as unread"
        );
      }
    },
    [apiCall]
  );

  const archiveNotification = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "ARCHIVED" },
          },
          `archive-${id}`
        );

        if (response.success) {
          const updatedNotifications = notificationsRef.current.map((notif) =>
            notif.id === id ? { ...notif, status: "ARCHIVED" as const } : notif
          );
          updateDataRef.current(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to archive notification"
        );
      }
    },
    [apiCall]
  );

  const deleteNotification = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          { method: "DELETE" },
          `delete-notification-${id}`
        );

        if (response.success) {
          const filteredNotifications = notificationsRef.current.filter(
            (notif) => notif.id !== id
          );
          updateDataRef.current(filteredNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete notification"
        );
      }
    },
    [apiCall]
  );

  const bulkMarkAsRead = useCallback(
    async (ids: string[]): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/bulk",
          {
            method: "PUT",
            body: { ids, action: "markRead" },
          },
          "bulk-mark-read"
        );

        if (response.success) {
          const updatedNotifications = notificationsRef.current.map((notif) =>
            ids.includes(notif.id)
              ? {
                  ...notif,
                  status: "read" as const,
                  readAt: new Date().toISOString(),
                }
              : notif
          );
          updateDataRef.current(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to bulk mark as read"
        );
      }
    },
    [apiCall]
  );

  const bulkDelete = useCallback(
    async (ids: string[]): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/bulk",
          {
            method: "DELETE",
            body: { ids },
          },
          "bulk-delete"
        );

        if (response.success) {
          const filteredNotifications = notificationsRef.current.filter(
            (notif) => !ids.includes(notif.id)
          );
          updateDataRef.current(filteredNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to bulk delete"
        );
      }
    },
    [apiCall]
  );

  const getPreferences =
    useCallback(async (): Promise<NotificationPreferencesDto | null> => {
      try {
        const response = await apiCall<NotificationPreferencesDto>(
          "/api/admin/notifications/preferences",
          { method: "GET" },
          "get-preferences"
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get preferences"
        );
      }
    }, [apiCall]);

  const updatePreferences = useCallback(
    async (
      preferences: Partial<NotificationPreferencesDto>
    ): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/preferences",
          {
            method: "PUT",
            body: preferences,
          },
          "update-preferences"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update preferences"
        );
      }
    },
    [apiCall]
  );

  const sendEmailNotification = useCallback(
    async (to: string, subject: string, content: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/email",
          {
            method: "POST",
            body: { to, subject, content },
          },
          "send-email"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to send email"
        );
      }
    },
    [apiCall]
  );

  const searchNotifications = useCallback(
    (searchFilters: AdminNotificationsFilters) => {
      fetchNotifications(1, paginationRef.current.limit, searchFilters);
    },
    [fetchNotifications]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchNotifications(
        page,
        paginationRef.current.limit,
        filtersRef.current as AdminNotificationsFilters
      );
    },
    [fetchNotifications]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchNotifications(
        1,
        limit,
        filtersRef.current as AdminNotificationsFilters
      );
    },
    [fetchNotifications]
  );

  // Auto-fetch on mount if no data - use a ref to track if we've already fetched
  const hasFetched = useRef(false);

  useEffect(() => {
    if (
      notifications.length === 0 &&
      !loading &&
      !error &&
      !hasFetched.current
    ) {
      hasFetched.current = true;
      fetchNotifications();
    }
  }, [notifications.length, loading, error, fetchNotifications]);

  return {
    // Data
    notifications,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchNotifications,
    getNotificationById,
    markAsRead,
    markAsUnread,
    archiveNotification,
    deleteNotification,
    bulkMarkAsRead,
    bulkDelete,
    getPreferences,
    updatePreferences,
    sendEmailNotification,
    searchNotifications,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingNotification: (id: string) =>
      isOperationLoading(`get-notification-${id}`),
    isMarkingRead: (id: string) => isOperationLoading(`mark-read-${id}`),
    isMarkingUnread: (id: string) => isOperationLoading(`mark-unread-${id}`),
    isArchiving: (id: string) => isOperationLoading(`archive-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-notification-${id}`),
    isBulkMarkingRead: isOperationLoading("bulk-mark-read"),
    isBulkDeleting: isOperationLoading("bulk-delete"),
    isGettingPreferences: isOperationLoading("get-preferences"),
    isUpdatingPreferences: isOperationLoading("update-preferences"),
    isSendingEmail: isOperationLoading("send-email"),
  };
}
