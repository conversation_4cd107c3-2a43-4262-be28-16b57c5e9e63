"use client";

import { useState, useCallback, useEffect } from "react";
import { UserService } from "@/lib/services";
import { UserEntity } from "@/app/models/user.model";
import { Address, Product } from "@/types";
import { useAuth } from "@/providers/auth-provider";

interface UseUserState {
  user: UserEntity | null;
  loading: boolean;
  error: string | null;
}

// Main user hook
export function useUser() {
  const { isAuthenticated, isLoading: authLoading, queueRequest } = useAuth();
  const [state, setState] = useState<UseUserState>({
    user: null,
    loading: true,
    error: null,
  });

  const fetchUser = useCallback(async () => {
    if (!isAuthenticated) {
      setState({
        user: null,
        loading: false,
        error: null,
      });
      return;
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const response = (await queueRequest(() =>
        UserService.getProfile()
      )) as any;

      setState({
        user: response.data || null,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch user",
      }));
    }
  }, [isAuthenticated, queueRequest]);

  // Only fetch when auth is resolved and user is authenticated
  useEffect(() => {
    if (!authLoading) {
      fetchUser();
    }
  }, [authLoading, fetchUser]);

  const updateProfile = useCallback(
    async (data: any) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = (await queueRequest(() =>
          UserService.updateProfile(data)
        )) as any;

        setState((prev) => ({
          ...prev,
          user: response.data || null,
          loading: false,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error ? error.message : "Failed to update profile",
        }));
        throw error;
      }
    },
    [isAuthenticated, queueRequest]
  );

  const changePassword = useCallback(
    async (data: any) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = (await queueRequest(() =>
          UserService.changePassword(data)
        )) as any;

        setState((prev) => ({
          ...prev,
          loading: false,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to change password",
        }));
        throw error;
      }
    },
    [isAuthenticated, queueRequest]
  );

  const uploadAvatar = useCallback(
    async (file: File) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        const response = await UserService.uploadAvatar(file);

        setState((prev) => ({
          ...prev,
          user: prev.user ? { ...prev.user, ...response.data } : null,
          loading: false,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error ? error.message : "Failed to upload avatar",
        }));
        throw error;
      }
    },
    [isAuthenticated]
  );

  const refetch = useCallback(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    ...state,
    updateProfile,
    changePassword,
    uploadAvatar,
    refetch,
  };
}

// User addresses hook
export function useUserAddresses() {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAddresses = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await UserService.getAddresses();
      setAddresses(response.data || []);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch addresses"
      );
      setAddresses([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAddresses();
  }, [fetchAddresses]);

  const addAddress = useCallback(
    async (data: any) => {
      try {
        setLoading(true);
        setError(null);
        const response = await UserService.addAddress(data);

        // Refresh addresses list
        await fetchAddresses();

        return response;
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to add address"
        );
        throw error;
      }
    },
    [fetchAddresses]
  );

  const updateAddress = useCallback(
    async (id: string, data: any) => {
      try {
        setLoading(true);
        setError(null);
        const response = await UserService.updateAddress(id, data);

        // Refresh addresses list
        await fetchAddresses();

        return response;
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to update address"
        );
        throw error;
      }
    },
    [fetchAddresses]
  );

  const deleteAddress = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        setError(null);
        await UserService.deleteAddress(id);

        // Refresh addresses list
        await fetchAddresses();
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to delete address"
        );
        throw error;
      }
    },
    [fetchAddresses]
  );

  const setDefaultAddress = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        setError(null);
        await UserService.setDefaultAddress(id);

        // Refresh addresses list
        await fetchAddresses();
      } catch (error) {
        setError(
          error instanceof Error
            ? error.message
            : "Failed to set default address"
        );
        throw error;
      }
    },
    [fetchAddresses]
  );

  const refetch = useCallback(() => {
    fetchAddresses();
  }, [fetchAddresses]);

  return {
    addresses,
    loading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    refetch,
  };
}

// User wishlist hook
export function useUserWishlist() {
  const [wishlist, setWishlist] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWishlist = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await UserService.getWishlist();
      setWishlist(response.data || []);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch wishlist"
      );
      setWishlist([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchWishlist();
  }, [fetchWishlist]);

  const addToWishlist = useCallback(
    async (productId: string) => {
      try {
        setLoading(true);
        setError(null);
        await UserService.addToWishlist(productId);

        // Refresh wishlist
        await fetchWishlist();
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to add to wishlist"
        );
        throw error;
      }
    },
    [fetchWishlist]
  );

  const removeFromWishlist = useCallback(
    async (productId: string) => {
      try {
        setLoading(true);
        setError(null);
        await UserService.removeFromWishlist(productId);

        // Refresh wishlist
        await fetchWishlist();
      } catch (error) {
        setError(
          error instanceof Error
            ? error.message
            : "Failed to remove from wishlist"
        );
        throw error;
      }
    },
    [fetchWishlist]
  );

  const toggleWishlist = useCallback(
    async (productId: string) => {
      try {
        setLoading(true);
        setError(null);
        await UserService.toggleWishlist(productId);

        // Refresh wishlist
        await fetchWishlist();
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to toggle wishlist"
        );
        throw error;
      }
    },
    [fetchWishlist]
  );

  const isInWishlist = useCallback(
    (productId: string) => {
      return wishlist.some(
        (item) => item.productId === productId || item.product?.id === productId
      );
    },
    [wishlist]
  );

  const refetch = useCallback(() => {
    fetchWishlist();
  }, [fetchWishlist]);

  return {
    wishlist,
    loading,
    error,
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    isInWishlist,
    refetch,
    count: wishlist.length,
  };
}

// User preferences hook
export function useUserPreferences() {
  const [preferences, setPreferences] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPreferences = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = { data: {} }; // UserService.getEmailPreferences not implemented
      setPreferences(response.data);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch preferences"
      );
      setPreferences(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPreferences();
  }, [fetchPreferences]);

  const updatePreferences = useCallback(async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await UserService.updateEmailPreferences(data);
      setPreferences(response.data);

      return response;
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to update preferences"
      );
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(() => {
    fetchPreferences();
  }, [fetchPreferences]);

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    refetch,
  };
}
