import { toast } from "@/lib/toast";

export const addToWishlist = async (productId: string): Promise<boolean> => {
  try {
    const response = await fetch("/api/wishlist", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ productId }),
    });

    const data = await response.json();

    if (response.ok) {
      toast.success("Đã thêm vào danh sách yêu thích");
      return true;
    } else {
      toast.error(
        data.error || "Có lỗi xảy ra khi thêm vào danh sách yêu thích"
      );
      return false;
    }
  } catch (error) {
    console.error("Add to wishlist error:", error);
    toast.error("Có lỗi xảy ra khi thêm vào danh sách yêu thích");
    return false;
  }
};

export const removeFromWishlist = async (
  productId: string
): Promise<boolean> => {
  try {
    const response = await fetch(`/api/wishlist/${productId}`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (response.ok) {
      toast.success("Đã xóa khỏi danh sách yêu thích");
      return true;
    } else {
      toast.error(
        data.error || "Có lỗi xảy ra khi xóa khỏi danh sách yêu thích"
      );
      return false;
    }
  } catch (error) {
    console.error("Remove from wishlist error:", error);
    toast.error("Có lỗi xảy ra khi xóa khỏi danh sách yêu thích");
    return false;
  }
};

export const checkIfInWishlist = async (
  productId: string
): Promise<boolean> => {
  try {
    const response = await fetch(`/api/wishlist/check/${productId}`);
    const data = await response.json();

    if (response.ok) {
      return data.inWishlist;
    }
    return false;
  } catch (error) {
    console.error("Check wishlist error:", error);
    return false;
  }
};

export const toggleWishlist = async (
  productId: string
): Promise<{
  action: "added" | "removed";
  success: boolean;
}> => {
  try {
    const response = await fetch("/api/wishlist/toggle", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ productId }),
    });

    const data = await response.json();

    if (response.ok) {
      const action = data.action;
      if (action === "added") {
        toast.success("Đã thêm vào danh sách yêu thích");
      } else {
        toast.success("Đã xóa khỏi danh sách yêu thích");
      }
      return { action, success: true };
    } else {
      toast.error(
        data.error || "Có lỗi xảy ra khi thao tác với danh sách yêu thích"
      );
      return { action: "removed", success: false };
    }
  } catch (error) {
    console.error("Toggle wishlist error:", error);
    toast.error("Có lỗi xảy ra khi thao tác với danh sách yêu thích");
    return { action: "removed", success: false };
  }
};
