"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface GlobalSeoSettings {
  id: string;
  siteName?: string;
  siteDescription?: string;
  siteKeywords: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: any;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SeoData {
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl?: string;
  noindex?: boolean;
  nofollow?: boolean;
}

export function useSeo() {
  const [loading, setLoading] = useState(false);

  const getGlobalSettings = useCallback(async (): Promise<GlobalSeoSettings | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/seo/global");
      if (!response.ok) throw new Error("Failed to fetch global SEO settings");

      const data = await response.json();
      return data.settings || null;
    } catch (err) {
      const errorMessage = "Không thể tải cài đặt SEO";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateGlobalSettings = useCallback(async (settings: Partial<GlobalSeoSettings>): Promise<GlobalSeoSettings | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/seo/global", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) throw new Error("Failed to update global SEO settings");

      const data = await response.json();
      toast.success("Cập nhật cài đặt SEO thành công");
      return data.settings || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật cài đặt SEO";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const generateSitemap = useCallback(async (): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/seo/sitemap", {
        method: "POST",
      });

      if (!response.ok) throw new Error("Failed to generate sitemap");

      toast.success("Tạo sitemap thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể tạo sitemap";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const validateSeoData = useCallback((seoData: SeoData) => {
    const errors: string[] = [];

    if (seoData.metaTitle && seoData.metaTitle.length > 60) {
      errors.push("Meta title không nên dài quá 60 ký tự");
    }

    if (seoData.metaDescription && seoData.metaDescription.length > 160) {
      errors.push("Meta description không nên dài quá 160 ký tự");
    }

    if (seoData.keywords && seoData.keywords.length > 10) {
      errors.push("Không nên có quá 10 từ khóa");
    }

    return errors;
  }, []);

  return {
    loading,
    getGlobalSettings,
    updateGlobalSettings,
    generateSitemap,
    validateSeoData,
  };
}