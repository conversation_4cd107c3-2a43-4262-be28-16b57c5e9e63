import { Metadata } from "next";
import { Header, Footer } from "@/components/layout";
import { usePage } from "@/hooks/use-page";

export const metadata: Metadata = {
  title: "Câu Hỏi Thường Gặp - NS Shop | Hỗ Trợ Khách Hàng",
  description:
    "Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ may gia công, quy trình đặt hàng, vận chuyển và thanh toán tại NS Shop. Hỗ trợ khách hàng 24/7.",
  keywords: [
    "FAQ",
    "câu hỏi thường gặp",
    "hỗ trợ khách hàng",
    "may gia công",
    "NS Shop",
    "quy trình đặt hàng",
    "vận chuyển",
    "thanh toán",
  ],
  openGraph: {
    title: "Câu Hỏi Thường Gặp - NS Shop | Hỗ Trợ Khách Hàng",
    description:
      "Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ may gia công, quy trình đặt hàng, vận chuyển và thanh toán tại NS Shop.",
    images: ["/og-image-faq.jpg"],
    url: "https://nsshop.com/faq",
  },
  alternates: {
    canonical: "https://nsshop.com/faq",
  },
};

export default function FAQPage() {
  const { page, isLoading, isError } = usePage({ slug: "faq" });

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Đang tải trang...</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Không thể tải trang. Vui lòng thử lại sau.</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (!page) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Không tìm thấy trang.</p>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        <section className="py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none text-center mb-8">
              {page.title}
            </h1>
            <div
              className="prose lg:prose-xl mx-auto"
              dangerouslySetInnerHTML={{ __html: page.content as string }}
            />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}