import { Metadata } from "next";
import { Header, Footer } from "@/components/layout";
import { usePage } from "@/hooks/use-page";

export const metadata: Metadata = {
  title: "Về Chúng Tôi - NS Shop | Xưởng May Thời Trang Chuyên Nghiệp",
  description:
    "NS Shop - Xưởng may thời trang chuyên nghiệp với hơn 10 năm kinh nghiệm. Chuyên sản xuất quần áo thiết kế số lượng ít, chất lượng cao, giá cả cạnh tranh. Đối tác tin cậy của các thương hiệu thời trang.",
  keywords: [
    "xưởng may",
    "may gia công",
    "thời trang thiết kế",
    "sản xuất quần áo",
    "NS Shop",
    "may số lượng ít",
  ],
  openGraph: {
    title: "Về Chúng Tôi - NS Shop | Xưởng May Thời Trang Chu<PERSON>ê<PERSON>",
    description:
      "NS Shop - Xưởng may thời trang chuyên nghiệp với hơn 10 năm kinh nghiệm. Chuyên sản xuất quần áo thiết kế số lượng ít, chất lượng cao.",
    images: ["/og-image-about.jpg"],
    url: "https://nsshop.com/about-us",
  },
  alternates: {
    canonical: "https://nsshop.com/about-us",
  },
};

export default function AboutUsPage() {
  const { page, isLoading, isError } = usePage({ slug: "about-us" });

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Đang tải trang...</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Không thể tải trang. Vui lòng thử lại sau.</p>
        </main>
        <Footer />
      </div>
    );
  }

  if (!page) {
    return (
      <div className="min-h-screen flex flex-col bg-white">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <p>Không tìm thấy trang.</p>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        <section className="py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none text-center mb-8">
              {page.title}
            </h1>
            <div
              className="prose lg:prose-xl mx-auto"
              dangerouslySetInnerHTML={{ __html: page.content as string }}
            />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}