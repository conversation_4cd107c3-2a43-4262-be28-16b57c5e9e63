/**
 * SMTP Config Model
 * Business entity cho SMTP Configuration
 */

import { BaseEntity, PaginationOptions } from "./common.model";

/**
 * SMTP Config Entity
 * Phản ánh cấu trúc trong schema.prisma
 */
export interface SMTPConfigEntity extends BaseEntity {
  name: string; // Configuration name
  host: string; // SMTP host
  port: number; // SMTP port
  secure: boolean; // Use SSL/TLS
  username: string; // SMTP username
  password: string; // SMTP password (encrypted)
  fromName: string; // Default sender name
  fromEmail: string; // Default sender email
  isActive: boolean;
  isDefault: boolean;
}

/**
 * Create SMTP Config Data
 */
export interface CreateSMTPConfigData {
  name: string;
  host: string;
  port: number;
  secure?: boolean;
  username: string;
  password: string;
  fromName: string;
  fromEmail: string;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * Update SMTP Config Data
 */
export interface UpdateSMTPConfigData {
  name?: string;
  host?: string;
  port?: number;
  secure?: boolean;
  username?: string;
  password?: string;
  fromName?: string;
  fromEmail?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * SMTP Config Search Options
 */
export interface SMTPConfigSearchOptions extends PaginationOptions {
  isActive?: boolean;
  isDefault?: boolean;
  search?: string; // Search in name, host, fromEmail
}

/**
 * SMTP Test Result
 */
export interface SMTPTestResult {
  success: boolean;
  message: string;
  error?: string;
  responseTime?: number; // in milliseconds
}

/**
 * SMTP Config Business Rules
 */
export class SMTPConfigBusinessRules {
  /**
   * Validate SMTP host
   */
  static validateHost(host: string): boolean {
    // Basic hostname validation
    const hostRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    return hostRegex.test(host);
  }

  /**
   * Validate SMTP port
   */
  static validatePort(port: number): boolean {
    return port > 0 && port <= 65535;
  }

  /**
   * Validate email address
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate configuration name
   */
  static validateName(name: string): boolean {
    return name.length > 0 && name.length <= 255;
  }

  /**
   * Get common SMTP ports
   */
  static getCommonPorts(): { port: number; description: string; secure: boolean }[] {
    return [
      { port: 25, description: "SMTP (Non-encrypted)", secure: false },
      { port: 587, description: "SMTP (STARTTLS)", secure: true },
      { port: 465, description: "SMTPS (SSL/TLS)", secure: true },
      { port: 2525, description: "Alternative SMTP", secure: false },
    ];
  }

  /**
   * Get recommended settings for common providers
   */
  static getProviderSettings(provider: string): Partial<CreateSMTPConfigData> | null {
    const providers: Record<string, Partial<CreateSMTPConfigData>> = {
      gmail: {
        name: "Gmail SMTP",
        host: "smtp.gmail.com",
        port: 587,
        secure: true,
      },
      outlook: {
        name: "Outlook SMTP",
        host: "smtp-mail.outlook.com",
        port: 587,
        secure: true,
      },
      yahoo: {
        name: "Yahoo SMTP",
        host: "smtp.mail.yahoo.com",
        port: 587,
        secure: true,
      },
      sendgrid: {
        name: "SendGrid SMTP",
        host: "smtp.sendgrid.net",
        port: 587,
        secure: true,
        username: "apikey",
      },
      mailgun: {
        name: "Mailgun SMTP",
        host: "smtp.mailgun.org",
        port: 587,
        secure: true,
      },
      ses: {
        name: "Amazon SES",
        host: "email-smtp.us-east-1.amazonaws.com",
        port: 587,
        secure: true,
      },
    };

    return providers[provider.toLowerCase()] || null;
  }

  /**
   * Validate complete SMTP configuration
   */
  static validateConfig(config: CreateSMTPConfigData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateName(config.name)) {
      errors.push("Configuration name is required and must be less than 255 characters");
    }

    if (!this.validateHost(config.host)) {
      errors.push("Invalid SMTP host format");
    }

    if (!this.validatePort(config.port)) {
      errors.push("SMTP port must be between 1 and 65535");
    }

    if (!config.username || config.username.length === 0) {
      errors.push("SMTP username is required");
    }

    if (!config.password || config.password.length === 0) {
      errors.push("SMTP password is required");
    }

    if (!this.validateEmail(config.fromEmail)) {
      errors.push("Invalid sender email format");
    }

    if (!config.fromName || config.fromName.length === 0) {
      errors.push("Sender name is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Encrypt password (placeholder - should use proper encryption)
   */
  static encryptPassword(password: string): string {
    // In a real application, use proper encryption
    // This is just a placeholder
    return Buffer.from(password).toString('base64');
  }

  /**
   * Decrypt password (placeholder - should use proper decryption)
   */
  static decryptPassword(encryptedPassword: string): string {
    // In a real application, use proper decryption
    // This is just a placeholder
    try {
      return Buffer.from(encryptedPassword, 'base64').toString();
    } catch {
      return encryptedPassword; // Return as-is if not encrypted
    }
  }
}

/**
 * SMTP Config Factory
 */
export class SMTPConfigFactory {
  /**
   * Create default SMTP config
   */
  static createDefault(): CreateSMTPConfigData {
    return {
      name: "Default SMTP",
      host: "smtp.gmail.com",
      port: 587,
      secure: true,
      username: "",
      password: "",
      fromName: "NS Shop",
      fromEmail: "<EMAIL>",
      isActive: false,
      isDefault: false,
    };
  }

  /**
   * Create config from provider template
   */
  static createFromProvider(
    provider: string,
    username: string,
    password: string,
    fromEmail: string,
    fromName: string = "NS Shop"
  ): CreateSMTPConfigData | null {
    const template = SMTPConfigBusinessRules.getProviderSettings(provider);
    if (!template) {
      return null;
    }

    return {
      ...template,
      username,
      password: SMTPConfigBusinessRules.encryptPassword(password),
      fromEmail,
      fromName,
      isActive: false,
      isDefault: false,
    } as CreateSMTPConfigData;
  }

  /**
   * Create test config (for development)
   */
  static createTestConfig(): CreateSMTPConfigData {
    return {
      name: "Test SMTP (Ethereal)",
      host: "smtp.ethereal.email",
      port: 587,
      secure: false,
      username: "<EMAIL>",
      password: "test123",
      fromName: "NS Shop Test",
      fromEmail: "<EMAIL>",
      isActive: false,
      isDefault: false,
    };
  }
}
