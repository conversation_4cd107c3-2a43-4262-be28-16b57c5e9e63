/**
 * Wishlist Model
 * Business entity cho Wishlist - simplified to match database schema
 */

import { BaseEntity } from "./common.model";

/**
 * Wishlist Item Entity - matches database WishlistItem model
 */
export interface WishlistItemEntity extends BaseEntity {
  userId: string;
  productId: string;
}

/**
 * Wishlist Item with Relations
 */
export interface WishlistItemWithRelations extends WishlistItemEntity {
  product?: any; // ProductEntity
  user?: any; // UserEntity
}

/**
 * Create Wishlist Item Data
 */
export interface CreateWishlistItemData {
  userId: string;
  productId: string;
}

/**
 * Wishlist Business Rules
 */
export class WishlistBusinessRules {
  static readonly MAX_ITEMS_PER_USER = 100;
}
