/**
 * Email Template Model
 * Business entity cho Email Template
 */

import { BaseEntity, PaginationOptions } from "./common.model";
import { EmailTemplateType } from "@prisma/client";
import { Prisma } from "@prisma/client";

/**
 * Email Template Entity
 * Phản ánh cấu trúc trong schema.prisma
 */
export interface EmailTemplateEntity extends BaseEntity {
  name: string;
  subject: string;
  content: string; // HTML content
  type: EmailTemplateType;
  variables?: Prisma.JsonValue | null; // JSON field for template variables
  isActive: boolean;
  isDefault: boolean; // Default template for this type
  createdBy: string; // Admin who created the template
}

/**
 * Email Template with Relations
 */
export interface EmailTemplateWithRelations extends EmailTemplateEntity {
  _count?: {
    emailLogs: number;
  };
}

/**
 * Create Email Template Data
 */
export interface CreateEmailTemplateData {
  name: string;
  subject: string;
  content: string;
  type: EmailTemplateType;
  variables?: Prisma.JsonValue;
  isActive?: boolean;
  isDefault?: boolean;
  createdBy: string;
}

/**
 * Update Email Template Data
 */
export interface UpdateEmailTemplateData {
  name?: string;
  subject?: string;
  content?: string;
  type?: EmailTemplateType;
  variables?: Prisma.JsonValue;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * Email Template Search Options
 */
export interface EmailTemplateSearchOptions extends PaginationOptions {
  type?: EmailTemplateType;
  isActive?: boolean;
  isDefault?: boolean;
  createdBy?: string;
  search?: string; // Search in name, subject
}

/**
 * Email Template Business Rules
 */
export class EmailTemplateBusinessRules {
  /**
   * Validate template content has required variables
   */
  static validateTemplateVariables(
    content: string,
    variables: Record<string, any>
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!variables) {
      return { valid: true, errors };
    }

    // Extract variables from content ({{variableName}} format)
    const contentVariables = content.match(/\{\{([^}]+)\}\}/g) || [];
    const contentVarNames = contentVariables.map((v) =>
      v.replace(/\{\{|\}\}/g, "").trim()
    );

    // Check if all content variables are defined
    for (const varName of contentVarNames) {
      if (!variables.hasOwnProperty(varName)) {
        errors.push(
          `Variable '${varName}' used in content but not defined in variables`
        );
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate template subject
   */
  static validateSubject(subject: string): boolean {
    return subject.length > 0 && subject.length <= 255;
  }

  /**
   * Validate template name
   */
  static validateName(name: string): boolean {
    return name.length > 0 && name.length <= 255;
  }

  /**
   * Validate template data
   */
  static validateTemplate(templateData: {
    name: string;
    subject: string;
    content: string;
    type: EmailTemplateType;
    variables: Prisma.JsonValue;
    isActive: boolean;
    isDefault: boolean;
    createdBy: string;
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Name validation
    if (!templateData.name || templateData.name.trim().length === 0) {
      errors.push("Template name is required");
    } else if (templateData.name.length > 255) {
      errors.push("Template name must be less than 255 characters");
    }

    // Subject validation
    if (!templateData.subject || templateData.subject.trim().length === 0) {
      errors.push("Template subject is required");
    } else if (templateData.subject.length > 500) {
      errors.push("Template subject must be less than 500 characters");
    }

    // Content validation
    if (!templateData.content || templateData.content.trim().length === 0) {
      errors.push("Template content is required");
    }

    // CreatedBy validation
    if (!templateData.createdBy || templateData.createdBy.trim().length === 0) {
      errors.push("CreatedBy is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if template can be set as default
   */
  static canSetAsDefault(
    _type: EmailTemplateType,
    _isDefault: boolean
  ): boolean {
    // Only one template per type can be default
    return true; // This will be enforced at repository level
  }

  /**
   * Get default variables for template type
   */
  static getDefaultVariables(type: EmailTemplateType): Record<string, any> {
    const baseVariables = {
      recipientName: "string",
      recipientEmail: "string",
    };

    switch (type) {
      case EmailTemplateType.WELCOME:
        return {
          ...baseVariables,
          loginUrl: "string",
          supportEmail: "string",
        };

      case EmailTemplateType.ORDER_CONFIRMATION:
        return {
          ...baseVariables,
          orderNumber: "string",
          orderTotal: "number",
          orderItems: "array",
          shippingAddress: "object",
          trackingUrl: "string",
        };

      case EmailTemplateType.ORDER_SHIPPED:
        return {
          ...baseVariables,
          orderNumber: "string",
          trackingNumber: "string",
          trackingUrl: "string",
          estimatedDelivery: "string",
        };

      case EmailTemplateType.ORDER_DELIVERED:
        return {
          ...baseVariables,
          orderNumber: "string",
          deliveredAt: "string",
          reviewUrl: "string",
        };

      case EmailTemplateType.PASSWORD_RESET:
        return {
          ...baseVariables,
          resetUrl: "string",
          expiresAt: "string",
        };

      case EmailTemplateType.NEWSLETTER:
        return {
          ...baseVariables,
          unsubscribeUrl: "string",
          newsletterContent: "string",
        };

      case EmailTemplateType.PROMOTION:
        return {
          ...baseVariables,
          promotionTitle: "string",
          promotionCode: "string",
          discountAmount: "number",
          expiresAt: "string",
          shopUrl: "string",
        };

      case EmailTemplateType.CUSTOM:
      default:
        return baseVariables;
    }
  }
}

/**
 * Email Template Factory
 */
export class EmailTemplateFactory {
  /**
   * Create default template for type
   */
  static createDefaultTemplate(
    type: EmailTemplateType,
    createdBy: string
  ): CreateEmailTemplateData {
    const variables = EmailTemplateBusinessRules.getDefaultVariables(type);

    switch (type) {
      case EmailTemplateType.WELCOME:
        return {
          name: "Welcome Email Template",
          subject: "Chào mừng {{recipientName}} đến với NS Shop!",
          content: `
            <h1>Chào mừng {{recipientName}}!</h1>
            <p>Cảm ơn bạn đã đăng ký tài khoản tại NS Shop.</p>
            <p>Bạn có thể đăng nhập tại: <a href="{{loginUrl}}">{{loginUrl}}</a></p>
            <p>Nếu cần hỗ trợ, vui lòng liên hệ: {{supportEmail}}</p>
          `,
          type,
          variables,
          isActive: true,
          isDefault: true,
          createdBy,
        };

      case EmailTemplateType.ORDER_CONFIRMATION:
        return {
          name: "Order Confirmation Template",
          subject: "Xác nhận đơn hàng #{{orderNumber}}",
          content: `
            <h1>Đơn hàng của bạn đã được xác nhận</h1>
            <p>Xin chào {{recipientName}},</p>
            <p>Đơn hàng #{{orderNumber}} của bạn đã được xác nhận với tổng tiền {{orderTotal}}đ.</p>
            <p>Chúng tôi sẽ xử lý và giao hàng trong thời gian sớm nhất.</p>
          `,
          type,
          variables,
          isActive: true,
          isDefault: true,
          createdBy,
        };

      default:
        return {
          name: `${type} Template`,
          subject: "{{recipientName}} - NS Shop",
          content: `
            <h1>Xin chào {{recipientName}}!</h1>
            <p>Đây là email từ NS Shop.</p>
          `,
          type,
          variables,
          isActive: true,
          isDefault: false,
          createdBy,
        };
    }
  }
}
