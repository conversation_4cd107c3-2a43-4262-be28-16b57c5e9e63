/**
 * SEO Model
 * Business entities cho SEO module
 */

import { BaseEntity } from "./common.model";

/**
 * SEO Settings Entity (Global SEO Settings)
 */
export interface SEOSettingsEntity extends BaseEntity {
  // Global SEO settings
  siteName?: string;
  siteDescription?: string;
  siteKeywords: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;

  // Social media settings
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;

  // Technical SEO
  robotsTxt?: string;
  sitemapEnabled: boolean;
  sitemapFrequency?: string;

  // Analytics
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;

  // Schema.org settings
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: Record<string, any>;

  // Search Console
  googleSiteVerification?: string;
  bingSiteVerification?: string;
}

/**
 * Page SEO Entity
 */
export interface PageSEOEntity extends BaseEntity {
  path: string;
  title?: string;
  description?: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  canonical?: string;
  noindex: boolean;
  nofollow: boolean;
  priority?: number;
  changefreq?: string;
  isActive: boolean;
}

/**
 * Create SEO Settings Data
 */
export interface CreateSEOSettingsData {
  siteName?: string;
  siteDescription?: string;
  siteKeywords?: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled?: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: Record<string, any>;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
}

/**
 * Update SEO Settings Data
 */
export interface UpdateSEOSettingsData extends Partial<CreateSEOSettingsData> {}

/**
 * Create Page SEO Data
 */
export interface CreatePageSEOData {
  path: string;
  title?: string;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  priority?: number;
  changefreq?: string;
  isActive?: boolean;
}

/**
 * Update Page SEO Data
 */
export interface UpdatePageSEOData extends Partial<CreatePageSEOData> {}

/**
 * SEO Business Rules
 */
export class SEOBusinessRules {
  static validateSEOSettings(data: CreateSEOSettingsData | UpdateSEOSettingsData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate site name
    if ("siteName" in data && data.siteName !== undefined) {
      if (data.siteName && data.siteName.length > 100) {
        errors.push("Site name must not exceed 100 characters");
      }
    }

    // Validate site description
    if ("siteDescription" in data && data.siteDescription !== undefined) {
      if (data.siteDescription && data.siteDescription.length > 500) {
        errors.push("Site description must not exceed 500 characters");
      }
    }

    // Validate default title
    if ("defaultTitle" in data && data.defaultTitle !== undefined) {
      if (data.defaultTitle && data.defaultTitle.length > 60) {
        errors.push("Default title should not exceed 60 characters for SEO");
      }
    }

    // Validate default description
    if ("defaultDescription" in data && data.defaultDescription !== undefined) {
      if (data.defaultDescription && data.defaultDescription.length > 160) {
        errors.push("Default description should not exceed 160 characters for SEO");
      }
    }

    // Validate email format
    if ("contactEmail" in data && data.contactEmail !== undefined) {
      if (data.contactEmail && !this.isValidEmail(data.contactEmail)) {
        errors.push("Invalid email format");
      }
    }

    // Validate sitemap frequency
    if ("sitemapFrequency" in data && data.sitemapFrequency !== undefined) {
      const validFrequencies = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"];
      if (data.sitemapFrequency && !validFrequencies.includes(data.sitemapFrequency)) {
        errors.push("Invalid sitemap frequency");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validatePageSEO(data: CreatePageSEOData | UpdatePageSEOData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate path
    if ("path" in data && data.path !== undefined) {
      if (!data.path?.trim()) {
        errors.push("Page path is required");
      } else if (!data.path.startsWith("/")) {
        errors.push("Page path must start with /");
      }
    }

    // Validate title length
    if ("title" in data && data.title !== undefined) {
      if (data.title && data.title.length > 60) {
        errors.push("Title should not exceed 60 characters for SEO");
      }
    }

    // Validate description length
    if ("description" in data && data.description !== undefined) {
      if (data.description && data.description.length > 160) {
        errors.push("Description should not exceed 160 characters for SEO");
      }
    }

    // Validate keywords count
    if ("keywords" in data && data.keywords !== undefined) {
      if (data.keywords && data.keywords.length > 10) {
        errors.push("Should not have more than 10 keywords");
      }
    }

    // Validate priority
    if ("priority" in data && data.priority !== undefined) {
      if (data.priority !== null && (data.priority < 0 || data.priority > 1)) {
        errors.push("Priority must be between 0.0 and 1.0");
      }
    }

    // Validate changefreq
    if ("changefreq" in data && data.changefreq !== undefined) {
      const validFrequencies = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"];
      if (data.changefreq && !validFrequencies.includes(data.changefreq)) {
        errors.push("Invalid change frequency");
      }
    }

    // Validate canonical URL
    if ("canonical" in data && data.canonical !== undefined) {
      if (data.canonical && !this.isValidUrl(data.canonical)) {
        errors.push("Invalid canonical URL format");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static generateMetaTitle(title?: string, siteName?: string, titleTemplate?: string): string {
    if (!title) return siteName || "";
    
    if (titleTemplate && titleTemplate.includes("%s")) {
      return titleTemplate.replace("%s", title);
    }
    
    if (siteName) {
      return `${title} | ${siteName}`;
    }
    
    return title;
  }

  static generateMetaDescription(description?: string, defaultDescription?: string): string {
    return description || defaultDescription || "";
  }

  static generateKeywords(keywords: string[], siteKeywords: string[] = []): string[] {
    const combined = [...keywords, ...siteKeywords];
    return [...new Set(combined)]; // Remove duplicates
  }

  static validateSitemapFrequency(frequency: string): boolean {
    const validFrequencies = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"];
    return validFrequencies.includes(frequency);
  }

  static validateTwitterCardType(cardType: string): boolean {
    const validTypes = ["summary", "summary_large_image", "app", "player"];
    return validTypes.includes(cardType);
  }

  static generateStructuredData(settings: SEOSettingsEntity): Record<string, any> {
    const structuredData: Record<string, any> = {
      "@context": "https://schema.org",
      "@type": settings.organizationType || "Organization",
      name: settings.organizationName || settings.siteName,
      url: settings.defaultImage,
    };

    if (settings.organizationLogo) {
      structuredData.logo = settings.organizationLogo;
    }

    if (settings.contactEmail) {
      structuredData.email = settings.contactEmail;
    }

    if (settings.contactPhone) {
      structuredData.telephone = settings.contactPhone;
    }

    if (settings.address) {
      structuredData.address = settings.address;
    }

    return structuredData;
  }

  static getDefaultSitemapFrequency(): string {
    return "weekly";
  }

  static getDefaultPriority(): number {
    return 0.5;
  }

  static getDefaultTwitterCard(): string {
    return "summary";
  }
}