/**
 * Models Index
 * Export tất cả models và entities
 */

// Core entities
export * from "./user.model";
export * from "./admin-user.model";
export * from "./product.model";
export * from "./category.model";
export * from "./brand.model";
export * from "./order.model";

// E-commerce entities
export * from "./cart.model";
export * from "./address.model";
export * from "./review.model";
export * from "./wishlist.model";
export * from "./admin-notification.model";

// Content entities
export * from "./post.model";
export * from "./page.model";
export * from "./media.model";
export * from "./menu.model";

// System entities
export * from "./setting.model";
export * from "./notification.model";
export * from "./audit-log.model";
export * from "./contact.model";
export * from "./seo.model";

// Email Marketing entities
export * from "./email-template.model";
export * from "./email-log.model";
export * from "./smtp-config.model";

// Advanced entities (placeholder - will be created later)
// export * from './attribute.model';
// export * from './inventory.model';
// export * from './promotion.model';

// Common types and interfaces
export * from "./common.model";
