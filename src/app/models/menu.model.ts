/**
 * Menu Model
 * Business entity cho Menu
 */

import { BaseEntity, Status } from "./common.model";

/**
 * Menu Entity
 */
export interface MenuEntity extends BaseEntity {
  name: string;
  location: string;
  description?: string;
  isActive: boolean;
}

/**
 * Menu Item Entity
 */
export interface MenuItemEntity extends BaseEntity {
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order: number;
  isActive: boolean;
}

/**
 * Menu Item Type (từ Prisma schema)
 */
export enum MenuItemType {
  LINK = "LINK",
  PAGE = "PAGE",
  CATEGORY = "CATEGORY", 
  PRODUCT = "PRODUCT",
  CUSTOM = "CUSTOM",
  SEPARATOR = "SEPARATOR",
}

/**
 * Menu with Items
 */
export interface MenuWithItems extends MenuEntity {
  items?: MenuItemWithChildren[];
}

/**
 * Menu Item with Children
 */
export interface MenuItemWithChildren extends MenuItemEntity {
  children?: MenuItemWithChildren[];
}

/**
 * Menu Tree Node
 */
export interface MenuTreeNode extends MenuItemEntity {
  children?: MenuTreeNode[];
  level?: number;
}

/**
 * Create Menu Data
 */
export interface CreateMenuData {
  name: string;
  location: string;
  description?: string;
  isActive?: boolean;
}

/**
 * Update Menu Data
 */
export interface UpdateMenuData {
  name?: string;
  location?: string;
  description?: string;
  isActive?: boolean;
}

/**
 * Create Menu Item Data
 */
export interface CreateMenuItemData {
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type?: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Update Menu Item Data
 */
export interface UpdateMenuItemData {
  parentId?: string;
  title?: string;
  url?: string;
  type?: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Menu Business Rules
 */
export class MenuBusinessRules {
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static buildMenuTree(items: MenuItemEntity[]): MenuItemWithChildren[] {
    const itemMap = new Map<string, MenuItemWithChildren>();
    const rootItems: MenuItemWithChildren[] = [];

    // Create map of all items
    items.forEach((item) => {
      itemMap.set(item.id, { ...item, children: [] });
    });

    // Build tree structure
    items.forEach((item) => {
      const menuItem = itemMap.get(item.id)!;

      if (item.parentId) {
        const parent = itemMap.get(item.parentId);
        if (parent) {
          parent.children!.push(menuItem);
        }
      } else {
        rootItems.push(menuItem);
      }
    });

    // Sort by order
    const sortItems = (items: MenuItemWithChildren[]) => {
      items.sort((a, b) => a.order - b.order);
      items.forEach((item) => {
        if (item.children && item.children.length > 0) {
          sortItems(item.children);
        }
      });
    };

    sortItems(rootItems);
    return rootItems;
  }

  static validateMenu(data: CreateMenuData | UpdateMenuData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate name
    if ("name" in data && data.name !== undefined) {
      if (!data.name?.trim()) {
        errors.push("Menu name is required");
      } else if (data.name.trim().length < 2) {
        errors.push("Menu name must be at least 2 characters");
      } else if (data.name.trim().length > 100) {
        errors.push("Menu name must not exceed 100 characters");
      }
    }

    // Validate location
    if ("location" in data && data.location !== undefined) {
      if (!data.location?.trim()) {
        errors.push("Menu location is required");
      } else if (data.location.trim().length > 50) {
        errors.push("Menu location must not exceed 50 characters");
      }
    }

    // Validate description
    if ("description" in data && data.description !== undefined) {
      if (data.description && data.description.length > 500) {
        errors.push("Menu description must not exceed 500 characters");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateMenuItem(data: CreateMenuItemData | UpdateMenuItemData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate title
    if ("title" in data && data.title !== undefined) {
      if (!data.title?.trim()) {
        errors.push("Menu item title is required");
      } else if (data.title.trim().length < 1) {
        errors.push("Menu item title must be at least 1 character");
      } else if (data.title.trim().length > 200) {
        errors.push("Menu item title must not exceed 200 characters");
      }
    }

    // Validate URL format if provided
    if ("url" in data && data.url) {
      if (!this.isValidUrl(data.url)) {
        errors.push("Invalid URL format");
      }
    }

    // Validate type
    if ("type" in data && data.type !== undefined) {
      if (!Object.values(MenuItemType).includes(data.type)) {
        errors.push("Invalid menu item type");
      }
    }

    // Validate target
    if ("target" in data && data.target !== undefined) {
      if (data.target && data.target.length > 20) {
        errors.push("Target must not exceed 20 characters");
      }
    }

    // Validate order
    if ("order" in data && data.order !== undefined) {
      if (data.order < 0) {
        errors.push("Order must be non-negative");
      }
    }

    // Validate cssClass
    if ("cssClass" in data && data.cssClass !== undefined) {
      if (data.cssClass && data.cssClass.length > 200) {
        errors.push("CSS class must not exceed 200 characters");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static isValidUrl(url: string): boolean {
    // Allow relative URLs, absolute URLs, and anchors
    if (url.startsWith("/") || url.startsWith("#")) {
      return true;
    }

    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static canDelete(
    menu: MenuEntity,
    itemCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (itemCount > 0) {
      return {
        canDelete: false,
        reason: "Cannot delete menu with existing menu items",
      };
    }

    return { canDelete: true };
  }

  static canDeleteMenuItem(
    item: MenuItemEntity,
    childrenCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (childrenCount > 0) {
      return {
        canDelete: false,
        reason: "Cannot delete menu item with children",
      };
    }

    return { canDelete: true };
  }

  static getTypeLabel(type: MenuItemType): string {
    const typeLabels: Record<MenuItemType, string> = {
      [MenuItemType.LINK]: "Liên kết",
      [MenuItemType.PAGE]: "Trang",
      [MenuItemType.CATEGORY]: "Danh mục",
      [MenuItemType.PRODUCT]: "Sản phẩm",
      [MenuItemType.CUSTOM]: "Tùy chỉnh",
      [MenuItemType.SEPARATOR]: "Phân cách",
    };

    return typeLabels[type] || type;
  }

  static getMaxDepth(): number {
    return 3; // Maximum nesting level for menu items
  }

  static validateDepth(
    parentId: string | undefined,
    items: MenuItemEntity[]
  ): boolean {
    if (!parentId) return true;

    let depth = 0;
    let currentParentId: string | undefined = parentId;

    while (currentParentId && depth < this.getMaxDepth()) {
      const parent = items.find((item) => item.id === currentParentId);
      if (!parent) break;

      currentParentId = parent.parentId;
      depth++;
    }

    return depth < this.getMaxDepth();
  }
}
