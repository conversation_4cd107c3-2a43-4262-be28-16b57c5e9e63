/**
 * Email Log Model
 * Business entity cho Email Log
 */

import { BaseEntity, PaginationOptions } from "./common.model";
import { EmailStatus } from "@prisma/client";

/**
 * Email Log Entity
 * Phản ánh cấu trúc trong schema.prisma
 */
export interface EmailLogEntity extends BaseEntity {
  templateId?: string | null;
  recipient: string; // Email recipient
  subject: string; // Email subject
  content: string; // Email content (HTML)
  status: EmailStatus;
  error?: string | null; // Error message if failed
  sentAt?: Date | null;
}

/**
 * Email Log with Relations
 */
export interface EmailLogWithRelations extends EmailLogEntity {
  template?: {
    id: string;
    name: string;
    type: string;
  } | null;
}

/**
 * Create Email Log Data
 */
export interface CreateEmailLogData {
  templateId?: string;
  recipient: string;
  subject: string;
  content: string;
  status?: EmailStatus;
  error?: string;
  sentAt?: Date;
}

/**
 * Update Email Log Data
 */
export interface UpdateEmailLogData {
  status?: EmailStatus;
  error?: string;
  sentAt?: Date;
}

/**
 * Email Log Search Options
 */
export interface EmailLogSearchOptions extends PaginationOptions {
  templateId?: string;
  recipient?: string;
  status?: EmailStatus;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string; // Search in recipient, subject
}

/**
 * Email Log Statistics
 */
export interface EmailLogStats {
  total: number;
  sent: number;
  failed: number;
  pending: number;
  bounced: number;
  successRate: number; // Percentage
  failureRate: number; // Percentage
}

/**
 * Email Log Business Rules
 */
export class EmailLogBusinessRules {
  /**
   * Validate email recipient
   */
  static validateRecipient(recipient: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(recipient);
  }

  /**
   * Validate email subject
   */
  static validateSubject(subject: string): boolean {
    return subject.length > 0 && subject.length <= 255;
  }

  /**
   * Check if email can be retried
   */
  static canRetry(status: EmailStatus): boolean {
    return status === EmailStatus.FAILED || status === EmailStatus.PENDING;
  }

  /**
   * Check if email is final status
   */
  static isFinalStatus(status: EmailStatus): boolean {
    return status === EmailStatus.SENT || status === EmailStatus.BOUNCED;
  }

  /**
   * Get status priority for sorting
   */
  static getStatusPriority(status: EmailStatus): number {
    switch (status) {
      case EmailStatus.FAILED:
        return 1;
      case EmailStatus.PENDING:
        return 2;
      case EmailStatus.BOUNCED:
        return 3;
      case EmailStatus.SENT:
        return 4;
      default:
        return 5;
    }
  }

  /**
   * Calculate statistics from email logs
   */
  static calculateStats(logs: EmailLogEntity[]): EmailLogStats {
    const total = logs.length;
    const sent = logs.filter(log => log.status === EmailStatus.SENT).length;
    const failed = logs.filter(log => log.status === EmailStatus.FAILED).length;
    const pending = logs.filter(log => log.status === EmailStatus.PENDING).length;
    const bounced = logs.filter(log => log.status === EmailStatus.BOUNCED).length;

    const successRate = total > 0 ? (sent / total) * 100 : 0;
    const failureRate = total > 0 ? ((failed + bounced) / total) * 100 : 0;

    return {
      total,
      sent,
      failed,
      pending,
      bounced,
      successRate: Math.round(successRate * 100) / 100,
      failureRate: Math.round(failureRate * 100) / 100,
    };
  }

  /**
   * Get status display text
   */
  static getStatusDisplayText(status: EmailStatus): string {
    switch (status) {
      case EmailStatus.PENDING:
        return "Đang chờ";
      case EmailStatus.SENT:
        return "Đã gửi";
      case EmailStatus.FAILED:
        return "Thất bại";
      case EmailStatus.BOUNCED:
        return "Bị trả về";
      default:
        return "Không xác định";
    }
  }

  /**
   * Get status color for UI
   */
  static getStatusColor(status: EmailStatus): string {
    switch (status) {
      case EmailStatus.PENDING:
        return "yellow";
      case EmailStatus.SENT:
        return "green";
      case EmailStatus.FAILED:
        return "red";
      case EmailStatus.BOUNCED:
        return "orange";
      default:
        return "gray";
    }
  }
}

/**
 * Email Log Factory
 */
export class EmailLogFactory {
  /**
   * Create email log from template and data
   */
  static createFromTemplate(
    templateId: string,
    recipient: string,
    subject: string,
    content: string
  ): CreateEmailLogData {
    return {
      templateId,
      recipient,
      subject,
      content,
      status: EmailStatus.PENDING,
    };
  }

  /**
   * Create email log for direct send
   */
  static createDirect(
    recipient: string,
    subject: string,
    content: string
  ): CreateEmailLogData {
    return {
      recipient,
      subject,
      content,
      status: EmailStatus.PENDING,
    };
  }

  /**
   * Mark as sent
   */
  static markAsSent(): UpdateEmailLogData {
    return {
      status: EmailStatus.SENT,
      sentAt: new Date(),
    };
  }

  /**
   * Mark as failed
   */
  static markAsFailed(error: string): UpdateEmailLogData {
    return {
      status: EmailStatus.FAILED,
      error,
    };
  }

  /**
   * Mark as bounced
   */
  static markAsBounced(error?: string): UpdateEmailLogData {
    return {
      status: EmailStatus.BOUNCED,
      error,
    };
  }
}
