/**
 * Admin Notification Model
 * Business entity cho Admin Notification system
 */

import { BaseEntity } from "./common.model";

/**
 * Admin Notification Type - phù hợp với Prisma schema
 */
export enum AdminNotificationType {
  INFO = "INFO",
  SUCCESS = "SUCCESS",
  WARNING = "WARNING", 
  ERROR = "ERROR",
  SYSTEM = "SYSTEM",
}

/**
 * Admin Notification Priority - phù hợp với Prisma schema
 */
export enum AdminNotificationPriority {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH",
  URGENT = "URGENT",
}

/**
 * Admin Notification Target - phù hợp với Prisma schema
 */
export enum AdminNotificationTarget {
  ALL_ADMINS = "ALL_ADMINS",
  SPECIFIC_ADMIN = "SPECIFIC_ADMIN",
  ROLE_ADMIN = "ROLE_ADMIN",
  ROLE_MODERATOR = "ROLE_MODERATOR",
}

/**
 * Admin User Entity for relations
 */
export interface AdminUserEntity {
  id: string;
  name: string;
  email: string;
  role: string;
}

/**
 * Admin Notification Entity - phù hợp với Prisma schema
 */
export interface AdminNotificationEntity extends BaseEntity {
  title: string;
  message: string;
  type: AdminNotificationType;
  priority: AdminNotificationPriority;
  targetType: AdminNotificationTarget;
  targetId?: string;
  isRead: boolean;
  readAt?: Date;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  createdBy?: string;
  creator?: AdminUserEntity;
  target?: AdminUserEntity;
}

/**
 * Create Admin Notification Data
 */
export interface CreateAdminNotificationData {
  title: string;
  message: string;
  type?: AdminNotificationType;
  priority?: AdminNotificationPriority;
  targetType?: AdminNotificationTarget;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  createdBy?: string;
}

/**
 * Update Admin Notification Data
 */
export interface UpdateAdminNotificationData {
  title?: string;
  message?: string;
  type?: AdminNotificationType;
  priority?: AdminNotificationPriority;
  targetType?: AdminNotificationTarget;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  isRead?: boolean;
  readAt?: Date;
}

/**
 * Admin Notification Business Rules
 */
export class AdminNotificationBusinessRules {
  /**
   * Mark notification as read
   */
  static markAsRead(notification: AdminNotificationEntity): UpdateAdminNotificationData {
    return {
      isRead: true,
      readAt: new Date(),
    };
  }

  /**
   * Mark notification as unread
   */
  static markAsUnread(notification: AdminNotificationEntity): UpdateAdminNotificationData {
    return {
      isRead: false,
      readAt: undefined,
    };
  }

  /**
   * Validate notification data
   */
  static validateNotification(data: CreateAdminNotificationData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate title
    if (!data.title?.trim()) {
      errors.push("Title is required");
    } else if (data.title.trim().length > 200) {
      errors.push("Title must not exceed 200 characters");
    }

    // Validate message
    if (!data.message?.trim()) {
      errors.push("Message is required");
    } else if (data.message.trim().length > 1000) {
      errors.push("Message must not exceed 1000 characters");
    }

    // Validate type
    if (data.type && !Object.values(AdminNotificationType).includes(data.type)) {
      errors.push("Invalid notification type");
    }

    // Validate priority
    if (data.priority && !Object.values(AdminNotificationPriority).includes(data.priority)) {
      errors.push("Invalid notification priority");
    }

    // Validate target type
    if (data.targetType && !Object.values(AdminNotificationTarget).includes(data.targetType)) {
      errors.push("Invalid notification target type");
    }

    // Validate targetId when targetType is SPECIFIC_ADMIN
    if (data.targetType === AdminNotificationTarget.SPECIFIC_ADMIN && !data.targetId) {
      errors.push("Target ID is required when targeting specific admin");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get type label for display
   */
  static getTypeLabel(type: AdminNotificationType): string {
    const typeLabels: Record<AdminNotificationType, string> = {
      [AdminNotificationType.INFO]: "Thông tin",
      [AdminNotificationType.SUCCESS]: "Thành công",
      [AdminNotificationType.WARNING]: "Cảnh báo",
      [AdminNotificationType.ERROR]: "Lỗi",
      [AdminNotificationType.SYSTEM]: "Hệ thống",
    };

    return typeLabels[type] || type;
  }

  /**
   * Get priority label for display
   */
  static getPriorityLabel(priority: AdminNotificationPriority): string {
    const priorityLabels: Record<AdminNotificationPriority, string> = {
      [AdminNotificationPriority.LOW]: "Thấp",
      [AdminNotificationPriority.NORMAL]: "Bình thường",
      [AdminNotificationPriority.HIGH]: "Cao",
      [AdminNotificationPriority.URGENT]: "Khẩn cấp",
    };

    return priorityLabels[priority] || priority;
  }

  /**
   * Get target type label for display
   */
  static getTargetTypeLabel(targetType: AdminNotificationTarget): string {
    const targetLabels: Record<AdminNotificationTarget, string> = {
      [AdminNotificationTarget.ALL_ADMINS]: "Tất cả Admin",
      [AdminNotificationTarget.SPECIFIC_ADMIN]: "Admin cụ thể",
      [AdminNotificationTarget.ROLE_ADMIN]: "Vai trò Admin",
      [AdminNotificationTarget.ROLE_MODERATOR]: "Vai trò Moderator",
    };

    return targetLabels[targetType] || targetType;
  }

  /**
   * Check if notification is expired
   */
  static isExpired(notification: AdminNotificationEntity): boolean {
    if (!notification.expiresAt) return false;
    return new Date() > notification.expiresAt;
  }

  /**
   * Check if notification should be shown to admin
   */
  static shouldShowToAdmin(
    notification: AdminNotificationEntity,
    adminId: string,
    adminRole: string
  ): boolean {
    // Don't show expired notifications
    if (this.isExpired(notification)) return false;

    // Check target type
    switch (notification.targetType) {
      case AdminNotificationTarget.ALL_ADMINS:
        return true;
      case AdminNotificationTarget.SPECIFIC_ADMIN:
        return notification.targetId === adminId;
      case AdminNotificationTarget.ROLE_ADMIN:
        return adminRole === "ADMIN";
      case AdminNotificationTarget.ROLE_MODERATOR:
        return adminRole === "MODERATOR";
      default:
        return false;
    }
  }

  /**
   * Get priority level for sorting
   */
  static getPriorityLevel(priority: AdminNotificationPriority): number {
    const priorityLevels: Record<AdminNotificationPriority, number> = {
      [AdminNotificationPriority.URGENT]: 4,
      [AdminNotificationPriority.HIGH]: 3,
      [AdminNotificationPriority.NORMAL]: 2,
      [AdminNotificationPriority.LOW]: 1,
    };

    return priorityLevels[priority] || 2;
  }

  /**
   * Create system notification
   */
  static createSystemNotification(
    title: string,
    message: string,
    priority: AdminNotificationPriority = AdminNotificationPriority.NORMAL,
    actionUrl?: string
  ): CreateAdminNotificationData {
    return {
      title,
      message,
      type: AdminNotificationType.SYSTEM,
      priority,
      targetType: AdminNotificationTarget.ALL_ADMINS,
      actionUrl,
    };
  }

  /**
   * Create error notification
   */
  static createErrorNotification(
    title: string,
    message: string,
    targetType: AdminNotificationTarget = AdminNotificationTarget.ALL_ADMINS,
    targetId?: string
  ): CreateAdminNotificationData {
    return {
      title,
      message,
      type: AdminNotificationType.ERROR,
      priority: AdminNotificationPriority.HIGH,
      targetType,
      targetId,
    };
  }

  /**
   * Create success notification
   */
  static createSuccessNotification(
    title: string,
    message: string,
    targetType: AdminNotificationTarget = AdminNotificationTarget.ALL_ADMINS,
    targetId?: string
  ): CreateAdminNotificationData {
    return {
      title,
      message,
      type: AdminNotificationType.SUCCESS,
      priority: AdminNotificationPriority.NORMAL,
      targetType,
      targetId,
    };
  }
}