/**
 * Email Logs API Routes
 * CRUD operations cho Email Logs
 */

import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { EmailLogService } from "@/app/api/services";
import {
  EmailLogSearchDto,
  CreateEmailLogDto,
  EmailLogSearchDtoSchema,
  CreateEmailLogDtoSchema,
} from "@/app/dto";

/**
 * GET /api/admin/emails/logs
 * Get all email logs with pagination and search
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse search parameters
    const searchDto: EmailLogSearchDto = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      templateId: searchParams.get("templateId") || undefined,
      recipient: searchParams.get("recipient") || undefined,
      status: searchParams.get("status") as any,
      dateFrom: searchParams.get("dateFrom") || undefined,
      dateTo: searchParams.get("dateTo") || undefined,
      search: searchParams.get("search") || undefined,
      sortBy: (searchParams.get("sortBy") as any) || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as any) || "desc",
    };

    // Validate search parameters
    const validation = EmailLogSearchDtoSchema.safeParse(searchDto);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid search parameters",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailLogService = container.resolve<EmailLogService>(
      SERVICE_IDENTIFIERS.EMAIL_LOG_SERVICE
    );

    // Get logs
    const result = await emailLogService.getLogs(validation.data);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error getting email logs:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get email logs",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/emails/logs
 * Create new email log
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = CreateEmailLogDtoSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid log data",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailLogService = container.resolve<EmailLogService>(
      SERVICE_IDENTIFIERS.EMAIL_LOG_SERVICE
    );

    // Create log
    const log = await emailLogService.createLog(validation.data);

    return NextResponse.json(
      {
        success: true,
        message: "Email log created successfully",
        data: log,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating email log:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to create email log",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
