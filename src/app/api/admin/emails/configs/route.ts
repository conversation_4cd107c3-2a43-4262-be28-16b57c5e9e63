/**
 * <PERSON>TP Configs API Routes
 * CRUD operations cho SMTP Configurations
 */

import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { SMTPConfigService } from "@/app/api/services";
import {
  SMTPConfigSearchDto,
  CreateSMTPConfigDto,
  SMTPConfigSearchDtoSchema,
  CreateSMTPConfigDtoSchema,
} from "@/app/dto";

/**
 * GET /api/admin/emails/configs
 * Get all SMTP configs with pagination and search
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse search parameters
    const searchDto: SMTPConfigSearchDto = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      isActive:
        searchParams.get("isActive") === "true"
          ? true
          : searchParams.get("isActive") === "false"
            ? false
            : undefined,
      isDefault:
        searchParams.get("isDefault") === "true"
          ? true
          : searchParams.get("isDefault") === "false"
            ? false
            : undefined,
      search: searchParams.get("search") || undefined,
      sortBy: (searchParams.get("sortBy") as any) || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as any) || "desc",
    };

    // Validate search parameters
    const validation = SMTPConfigSearchDtoSchema.safeParse(searchDto);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid search parameters",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const smtpConfigService = container.resolve<SMTPConfigService>(
      SERVICE_IDENTIFIERS.SMTP_CONFIG_SERVICE
    );

    // Get configs
    const result = await smtpConfigService.getConfigs(validation.data);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error getting SMTP configs:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get SMTP configs",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/emails/configs
 * Create new SMTP config
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = CreateSMTPConfigDtoSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid SMTP config data",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const smtpConfigService = container.resolve<SMTPConfigService>(
      SERVICE_IDENTIFIERS.SMTP_CONFIG_SERVICE
    );

    // Create config
    const config = await smtpConfigService.createConfig(validation.data);

    return NextResponse.json(
      {
        success: true,
        message: "SMTP config created successfully",
        data: config,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating SMTP config:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to create SMTP config",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
