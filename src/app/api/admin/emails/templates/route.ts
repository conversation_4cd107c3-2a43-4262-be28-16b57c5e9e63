/**
 * Email Templates API Routes
 * CRUD operations cho Email Templates
 */

import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { EmailTemplateService } from "@/app/api/services";
import {
  EmailTemplateSearchDto,
  CreateEmailTemplateDto,
  EmailTemplateSearchDtoSchema,
  CreateEmailTemplateDtoSchema,
} from "@/app/dto";

/**
 * GET /api/admin/emails/templates
 * Get all email templates with pagination and search
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse search parameters
    const searchDto: EmailTemplateSearchDto = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      type: searchParams.get("type") as any,
      isActive:
        searchParams.get("isActive") === "true"
          ? true
          : searchParams.get("isActive") === "false"
            ? false
            : undefined,
      isDefault:
        searchParams.get("isDefault") === "true"
          ? true
          : searchParams.get("isDefault") === "false"
            ? false
            : undefined,
      createdBy: searchParams.get("createdBy") || undefined,
      search: searchParams.get("search") || undefined,
      sortBy: (searchParams.get("sortBy") as any) || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as any) || "desc",
    };

    // Validate search parameters
    const validation = EmailTemplateSearchDtoSchema.safeParse(searchDto);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid search parameters",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailTemplateService = container.resolve<EmailTemplateService>(
      SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE
    );

    // Get templates
    const result = await emailTemplateService.getTemplates(validation.data);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error getting email templates:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get email templates",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/emails/templates
 * Create new email template
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = CreateEmailTemplateDtoSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid template data",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailTemplateService = container.resolve<EmailTemplateService>(
      SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE
    );

    // Create template
    const template = await emailTemplateService.createTemplate(validation.data);

    return NextResponse.json(
      {
        success: true,
        message: "Email template created successfully",
        data: template,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating email template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to create email template",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
