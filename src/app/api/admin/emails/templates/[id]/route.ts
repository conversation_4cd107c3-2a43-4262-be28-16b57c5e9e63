/**
 * Email Template Individual API Routes
 * CRUD operations cho individual Email Template
 */

import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import { EmailTemplateService } from "@/app/api/services";
import {
  UpdateEmailTemplateDto,
  UpdateEmailTemplateDtoSchema,
} from "@/app/dto";

/**
 * GET /api/admin/emails/templates/[id]
 * Get email template by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "Template ID is required",
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailTemplateService = container.resolve<EmailTemplateService>(
      SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE
    );

    // Get template
    const template = await emailTemplateService.getTemplateById(id);

    if (!template) {
      return NextResponse.json(
        {
          success: false,
          message: "Email template not found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error("Error getting email template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get email template",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/emails/templates/[id]
 * Update email template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "Template ID is required",
        },
        { status: 400 }
      );
    }

    // Validate request body
    const validation = UpdateEmailTemplateDtoSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid template data",
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailTemplateService = container.resolve<EmailTemplateService>(
      SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE
    );

    // Update template
    const template = await emailTemplateService.updateTemplate(
      id,
      validation.data
    );

    return NextResponse.json({
      success: true,
      message: "Email template updated successfully",
      data: template,
    });
  } catch (error) {
    console.error("Error updating email template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to update email template",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/emails/templates/[id]
 * Delete email template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: "Template ID is required",
        },
        { status: 400 }
      );
    }

    // Get service from DI container
    const emailTemplateService = container.resolve<EmailTemplateService>(
      SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE
    );

    // Delete template
    await emailTemplateService.deleteTemplate(id);

    return NextResponse.json({
      success: true,
      message: "Email template deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting email template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete email template",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
