import { NextRequest, NextResponse } from "next/server";
import { PageController } from "@/lib/admin/controllers/PageController";
import { verifyAdminToken } from "@/lib/admin-auth";
import { CreatePageDtoSchema } from "@/app/dto/page.dto";

const pageController = new PageController();

// GET /api/admin/pages - Get all pages with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const featured = searchParams.get("featured");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as "asc" | "desc";

    const params = {
      page,
      limit,
      search,
      status: status || undefined,
      featured: featured ? featured === "true" : undefined,
      sortBy,
      sortOrder,
    };

    const result = await pageController.getAll(params);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Get pages error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách trang" },
      { status: 500 }
    );
  }
}

// POST /api/admin/pages - Create new page
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = CreatePageDtoSchema.parse(body);
    
    // Add author ID from token
    const pageData = {
      ...validatedData,
      authorId: adminToken.id,
    };

    const result = await pageController.create(pageData);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Create page error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo trang" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/pages - Bulk delete pages
export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { ids } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const result = await pageController.bulkDelete(ids);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Bulk delete pages error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa trang" },
      { status: 500 }
    );
  }
}
