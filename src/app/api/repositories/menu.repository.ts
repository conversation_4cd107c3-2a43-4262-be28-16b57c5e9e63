/**
 * Menu Repository
 * Qu<PERSON>n lý các thao tác database cho Menu và MenuItem models
 */

import { Menu, MenuItem, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";
import {
  MenuDTO,
  MenuItemDTO,
  MenuWithItemsDTO,
  CreateMenuDTO,
  UpdateMenuDTO,
  CreateMenuItemDTO,
  UpdateMenuItemDTO,
  MenuSearchFiltersDTO,
  transformMenuToDTO,
  transformMenuItemToDTO,
  transformMenuWithItemsToDTO,
  transformCreateMenuDTOToPrisma,
  transformUpdateMenuDTOToPrisma,
  transformCreateMenuItemDTOToPrisma,
  transformUpdateMenuItemDTOToPrisma,
} from "../../dto";

export type MenuWithItems = Prisma.MenuGetPayload<{
  include: {
    items: {
      include: {
        children: {
          include: {
            children: true;
          };
        };
      };
    };
  };
}>;

export class MenuRepository extends BaseRepository<
  Menu,
  Prisma.MenuCreateInput,
  Prisma.MenuUpdateInput
> {
  constructor() {
    super("menu");
  }

  // Tìm menu theo location
  async findByLocation(location: string): Promise<MenuDTO | null> {
    const menu = await this.findFirst({ location });
    return menu ? transformMenuToDTO(menu) : null;
  }

  // Tìm menu với items
  async findByIdWithItems(id: string): Promise<MenuWithItemsDTO | null> {
    const menu = await this.model.findUnique({
      where: { id },
      include: {
        items: {
          where: { parentId: null },
          include: {
            children: {
              include: {
                children: true,
              },
              orderBy: { order: "asc" },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });
    return menu ? transformMenuWithItemsToDTO(menu) : null;
  }

  // Tìm menu theo location với items
  async findByLocationWithItems(
    location: string
  ): Promise<MenuWithItemsDTO | null> {
    const menu = await this.model.findFirst({
      where: { location, isActive: true },
      include: {
        items: {
          where: { parentId: null, isActive: true },
          include: {
            children: {
              where: { isActive: true },
              include: {
                children: {
                  where: { isActive: true },
                  orderBy: { order: "asc" },
                },
              },
              orderBy: { order: "asc" },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });
    return menu ? transformMenuWithItemsToDTO(menu) : null;
  }

  // Tạo menu mới
  async createMenu(data: CreateMenuDTO): Promise<MenuDTO> {
    // Kiểm tra location đã tồn tại
    const existingMenu = await this.findByLocation(data.location);
    if (existingMenu) {
      throw new ConflictError("Menu location đã tồn tại");
    }

    const prismaData = transformCreateMenuDTOToPrisma(data);
    const menu = await this.create(prismaData);
    return transformMenuToDTO(menu);
  }

  // Tìm kiếm menus với filter
  async searchMenus(options: MenuSearchFiltersDTO) {
    const { search, location, isActive, page = 1, limit = 20, sortBy = "name", sortOrder = "asc" } = options;

    const where: Prisma.MenuWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    const result = await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        _count: {
          select: { items: true },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });

    return {
      ...result,
      data: result.data.map(menu => transformMenuToDTO(menu)),
    };
  }

  // Lấy active menus
  async getActiveMenus(): Promise<MenuDTO[]> {
    const menus = await this.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { items: true },
        },
      },
      orderBy: { name: "asc" },
    });
    return menus.map(menu => transformMenuToDTO(menu));
  }

  // Activate menu
  async activateMenu(id: string): Promise<MenuDTO> {
    const menu = await this.update(id, { isActive: true });
    return transformMenuToDTO(menu);
  }

  // Deactivate menu
  async deactivateMenu(id: string): Promise<MenuDTO> {
    const menu = await this.update(id, { isActive: false });
    return transformMenuToDTO(menu);
  }

  // Lấy thống kê menus
  async getMenuStats() {
    const [total, active, inactive, totalItems] = await Promise.all([
      this.count(),
      this.count({ isActive: true }),
      this.count({ isActive: false }),
      this.prisma.menuItem.count(),
    ]);

    return {
      total,
      active,
      inactive,
      totalItems,
    };
  }

  // Update menu
  async updateMenu(id: string, data: UpdateMenuDTO): Promise<MenuDTO> {
    const menu = await this.findById(id);
    if (!menu) {
      throw new NotFoundError("Menu", id);
    }

    const prismaData = transformUpdateMenuDTOToPrisma(data);
    const updatedMenu = await this.update(id, prismaData);
    return transformMenuToDTO(updatedMenu);
  }

  // Menu Item Methods

  // Tạo menu item
  async createMenuItem(data: CreateMenuItemDTO): Promise<MenuItemDTO> {
    // Kiểm tra menu tồn tại
    const menu = await this.findById(data.menuId);
    if (!menu) {
      throw new NotFoundError("Menu", data.menuId);
    }

    // Kiểm tra parent item tồn tại (nếu có)
    if (data.parentId) {
      const parentItem = await this.prisma.menuItem.findUnique({
        where: { id: data.parentId },
      });
      if (!parentItem) {
        throw new NotFoundError("Parent MenuItem", data.parentId);
      }
    }

    const createData = transformCreateMenuItemDTOToPrisma(data);
    const menuItem = await this.prisma.menuItem.create({ data: createData });
    return transformMenuItemToDTO(menuItem);
  }

  // Cập nhật menu item
  async updateMenuItem(
    id: string,
    data: UpdateMenuItemDTO
  ): Promise<MenuItemDTO> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
    });
    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    // Kiểm tra parent item tồn tại (nếu có)
    if (data.parentId !== undefined) {
      if (data.parentId) {
        const parentItem = await this.prisma.menuItem.findUnique({
          where: { id: data.parentId },
        });
        if (!parentItem) {
          throw new NotFoundError("Parent MenuItem", data.parentId);
        }
      }
    }

    const prismaUpdateData = transformUpdateMenuItemDTOToPrisma(data);
    const updatedItem = await this.prisma.menuItem.update({
      where: { id },
      data: prismaUpdateData,
    });

    return transformMenuItemToDTO(updatedItem);
  }

  // Xóa menu item
  async deleteMenuItem(id: string): Promise<MenuItemDTO> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
      include: { children: true },
    });

    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    // Xóa children trước
    if (item.children.length > 0) {
      await this.prisma.menuItem.deleteMany({
        where: { parentId: id },
      });
    }

    const deletedItem = await this.prisma.menuItem.delete({
      where: { id },
    });

    return transformMenuItemToDTO(deletedItem);
  }

  // Lấy menu items của menu
  async getMenuItems(menuId: string): Promise<MenuItemDTO[]> {
    const items = await this.prisma.menuItem.findMany({
      where: { menuId },
      include: {
        children: {
          include: {
            children: true,
          },
          orderBy: { order: "asc" },
        },
      },
      orderBy: { order: "asc" },
    });
    return items.map(item => transformMenuItemToDTO(item));
  }

  // Reorder menu items
  async reorderMenuItems(
    itemOrders: Array<{ id: string; order: number }>
  ): Promise<void> {
    for (const { id, order } of itemOrders) {
      await this.prisma.menuItem.update({
        where: { id },
        data: { order },
      });
    }
  }

  // Move menu item to different parent
  async moveMenuItem(
    id: string,
    newParentId: string | null
  ): Promise<MenuItemDTO> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
    });

    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    // Kiểm tra không tạo circular reference
    if (newParentId) {
      const breadcrumb = await this.getMenuItemBreadcrumb(newParentId);
      if (breadcrumb.some((breadcrumbItem) => breadcrumbItem.id === id)) {
        throw new ConflictError(
          "Không thể di chuyển menu item thành con của chính nó"
        );
      }
    }

    const updatedItem = await this.prisma.menuItem.update({
      where: { id },
      data: { parentId: newParentId },
    });

    return transformMenuItemToDTO(updatedItem);
  }

  // Lấy breadcrumb của menu item
  async getMenuItemBreadcrumb(itemId: string): Promise<MenuItemDTO[]> {
    const path: MenuItemDTO[] = [];
    let currentId: string | null = itemId;

    while (currentId) {
      const item: MenuItem | null = await this.prisma.menuItem.findUnique({
        where: { id: currentId },
      });
      if (!item) break;

      path.unshift(transformMenuItemToDTO(item));
      currentId = item.parentId;
    }

    return path;
  }

  // Duplicate menu
  async duplicateMenu(id: string): Promise<MenuDTO> {
    const menu = await this.findByIdWithItems(id);
    if (!menu) {
      throw new NotFoundError("Menu", id);
    }

    // Tạo menu mới
    const createData: CreateMenuDTO = {
      name: `${menu.name} (Copy)`,
      location: `${menu.location}-copy-${Date.now()}`,
      description: menu.description,
      isActive: false,
    };

    const newMenu = await this.createMenu(createData);

    // Duplicate menu items
    await this.duplicateMenuItems(menu.items, newMenu.id);

    return newMenu;
  }

  // Duplicate menu items (recursive)
  private async duplicateMenuItems(
    items: any[],
    menuId: string,
    parentId: string | null = null
  ): Promise<void> {
    for (const item of items) {
      const createData: CreateMenuItemDTO = {
        menuId,
        parentId: parentId || undefined,
        title: item.title,
        url: item.url,
        type: item.type,
        target: item.target,
        icon: item.icon,
        cssClass: item.cssClass,
        order: item.order,
        isActive: item.isActive,
      };

      const newItem = await this.createMenuItem(createData);

      // Duplicate children
      if (item.children && item.children.length > 0) {
        await this.duplicateMenuItems(item.children, menuId, newItem.id);
      }
    }
  }

  // Validate menu structure
  async validateMenuStructure(menuId: string): Promise<{
    valid: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    const menu = await this.findByIdWithItems(menuId);

    if (!menu) {
      return { valid: false, issues: ["Menu không tồn tại"] };
    }

    // Kiểm tra circular references
    const checkCircularRef = (
      items: any[],
      visited: Set<string> = new Set()
    ): void => {
      for (const item of items) {
        if (visited.has(item.id)) {
          issues.push(`Circular reference detected for item: ${item.title}`);
          continue;
        }

        visited.add(item.id);
        if (item.children) {
          checkCircularRef(item.children, new Set(visited));
        }
        visited.delete(item.id);
      }
    };

    checkCircularRef(menu.items);

    // Kiểm tra URL format
    const checkUrls = (items: any[]): void => {
      for (const item of items) {
        if (
          item.url &&
          !item.url.startsWith("/") &&
          !item.url.startsWith("http")
        ) {
          issues.push(`Invalid URL format for item: ${item.title}`);
        }

        if (item.children) {
          checkUrls(item.children);
        }
      }
    };

    checkUrls(menu.items);

    return {
      valid: issues.length === 0,
      issues,
    };
  }
}
