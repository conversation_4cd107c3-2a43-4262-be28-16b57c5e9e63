/**
 * Email Log Repository
 * Data access layer cho Email Log
 */

import { EmailLog, EmailStatus, Prisma } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import {
  EmailLogEntity,
  EmailLogWithRelations,
  CreateEmailLogData,
  UpdateEmailLogData,
  EmailLogSearchOptions,
  EmailLogStats,
} from "@/app/models";

type EmailLogCreateInput = Prisma.EmailLogCreateInput;
type EmailLogUpdateInput = Prisma.EmailLogUpdateInput;
type EmailLogWithTemplate = EmailLog & {
  template?: {
    id: string;
    name: string;
    type: string;
  } | null;
};

export class EmailLogRepository extends BaseRepository<
  EmailLog,
  EmailLogCreateInput,
  EmailLogUpdateInput
> {
  constructor() {
    super("emailLog");
  }

  /**
   * Find logs by template ID
   */
  async findByTemplateId(templateId: string): Promise<EmailLogEntity[]> {
    const logs = await this.model.findMany({
      where: { templateId },
      orderBy: { createdAt: "desc" },
    });

    return logs;
  }

  /**
   * Find logs by recipient
   */
  async findByRecipient(recipient: string): Promise<EmailLogEntity[]> {
    const logs = await this.model.findMany({
      where: { recipient },
      orderBy: { createdAt: "desc" },
    });

    return logs;
  }

  /**
   * Find logs by status
   */
  async findByStatus(status: EmailStatus): Promise<EmailLogEntity[]> {
    const logs = await this.model.findMany({
      where: { status },
      orderBy: { createdAt: "desc" },
    });

    return logs;
  }

  /**
   * Search logs with pagination
   */
  async search(options: EmailLogSearchOptions) {
    const {
      page = 1,
      limit = 20,
      templateId,
      recipient,
      status,
      dateFrom,
      dateTo,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.EmailLogWhereInput = {};

    if (templateId) {
      where.templateId = templateId;
    }

    if (recipient) {
      where.recipient = { contains: recipient, mode: "insensitive" };
    }

    if (status) {
      where.status = status;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = dateFrom;
      }
      if (dateTo) {
        where.createdAt.lte = dateTo;
      }
    }

    if (search) {
      where.OR = [
        { recipient: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    const orderBy: Prisma.EmailLogOrderByWithRelationInput =
      sortBy === "createdAt"
        ? { createdAt: sortOrder }
        : sortBy === "updatedAt"
          ? { updatedAt: sortOrder }
          : sortBy === "sentAt"
            ? { sentAt: sortOrder }
            : sortBy === "status"
              ? { status: sortOrder }
              : sortBy === "recipient"
                ? { recipient: sortOrder }
                : { createdAt: sortOrder };

    return await this.findWithPagination({
      where,
      orderBy,
      include: {
        template: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      page,
      limit,
    });
  }

  /**
   * Find logs with template information
   */
  async findWithTemplate(): Promise<EmailLogWithTemplate[]> {
    return await this.model.findMany({
      include: {
        template: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Create email log
   */
  async createLog(data: CreateEmailLogData): Promise<EmailLogEntity> {
    const log = await this.model.create({
      data: {
        templateId: data.templateId,
        recipient: data.recipient,
        subject: data.subject,
        content: data.content,
        status: data.status || EmailStatus.PENDING,
        error: data.error,
        sentAt: data.sentAt,
      },
    });

    return log;
  }

  /**
   * Update log status
   */
  async updateStatus(
    id: string,
    status: EmailStatus,
    error?: string,
    sentAt?: Date
  ): Promise<EmailLogEntity> {
    const updateData: Prisma.EmailLogUpdateInput = {
      status,
    };

    if (error !== undefined) {
      updateData.error = error;
    }

    if (sentAt !== undefined) {
      updateData.sentAt = sentAt;
    } else if (status === EmailStatus.SENT) {
      updateData.sentAt = new Date();
    }

    const log = await this.model.update({
      where: { id },
      data: updateData,
    });

    return log;
  }

  /**
   * Mark as sent
   */
  async markAsSent(id: string): Promise<EmailLogEntity> {
    return await this.updateStatus(id, EmailStatus.SENT, undefined, new Date());
  }

  /**
   * Mark as failed
   */
  async markAsFailed(id: string, error: string): Promise<EmailLogEntity> {
    return await this.updateStatus(id, EmailStatus.FAILED, error);
  }

  /**
   * Mark as bounced
   */
  async markAsBounced(id: string, error?: string): Promise<EmailLogEntity> {
    return await this.updateStatus(id, EmailStatus.BOUNCED, error);
  }

  /**
   * Get email statistics
   */
  async getStats(dateFrom?: Date, dateTo?: Date): Promise<EmailLogStats> {
    const where: Prisma.EmailLogWhereInput = {};

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = dateFrom;
      }
      if (dateTo) {
        where.createdAt.lte = dateTo;
      }
    }

    const [total, sent, failed, pending, bounced] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, status: EmailStatus.SENT } }),
      this.model.count({ where: { ...where, status: EmailStatus.FAILED } }),
      this.model.count({ where: { ...where, status: EmailStatus.PENDING } }),
      this.model.count({ where: { ...where, status: EmailStatus.BOUNCED } }),
    ]);

    const successRate = total > 0 ? (sent / total) * 100 : 0;
    const failureRate = total > 0 ? ((failed + bounced) / total) * 100 : 0;

    return {
      total,
      sent,
      failed,
      pending,
      bounced,
      successRate: Math.round(successRate * 100) / 100,
      failureRate: Math.round(failureRate * 100) / 100,
    };
  }

  /**
   * Get recent activity
   */
  async getRecentActivity(limit: number = 10): Promise<EmailLogEntity[]> {
    return await this.model.findMany({
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  /**
   * Get top failure reasons
   */
  async getTopFailureReasons(
    limit: number = 5
  ): Promise<{ error: string; count: number }[]> {
    const logs = await this.model.findMany({
      where: {
        status: { in: [EmailStatus.FAILED, EmailStatus.BOUNCED] },
        error: { not: null },
      },
      select: {
        error: true,
      },
    });

    // Group by error manually
    const errorCounts: Record<string, number> = {};
    logs.forEach((log) => {
      const error = log.error || "Unknown error";
      errorCounts[error] = (errorCounts[error] || 0) + 1;
    });

    // Convert to array and sort by count
    const result = Object.entries(errorCounts)
      .map(([error, count]) => ({ error, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    return result;
  }

  /**
   * Find logs that can be retried
   */
  async findRetryable(): Promise<EmailLogEntity[]> {
    return await this.model.findMany({
      where: {
        status: { in: [EmailStatus.FAILED, EmailStatus.PENDING] },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Bulk retry failed emails
   */
  async bulkRetry(ids: string[]): Promise<number> {
    const result = await this.model.updateMany({
      where: {
        id: { in: ids },
        status: { in: [EmailStatus.FAILED, EmailStatus.PENDING] },
      },
      data: {
        status: EmailStatus.PENDING,
        error: null,
        sentAt: null,
      },
    });

    return result.count;
  }

  /**
   * Clean old logs
   */
  async cleanOldLogs(olderThanDays: number): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.model.deleteMany({
      where: {
        createdAt: { lt: cutoffDate },
        status: { in: [EmailStatus.SENT, EmailStatus.BOUNCED] },
      },
    });

    return result.count;
  }

  /**
   * Get logs for export
   */
  async getLogsForExport(options: {
    dateFrom?: Date;
    dateTo?: Date;
    status?: EmailStatus;
    templateId?: string;
  }): Promise<EmailLogWithTemplate[]> {
    const where: Prisma.EmailLogWhereInput = {};

    if (options.dateFrom || options.dateTo) {
      where.createdAt = {};
      if (options.dateFrom) {
        where.createdAt.gte = options.dateFrom;
      }
      if (options.dateTo) {
        where.createdAt.lte = options.dateTo;
      }
    }

    if (options.status) {
      where.status = options.status;
    }

    if (options.templateId) {
      where.templateId = options.templateId;
    }

    return await this.model.findMany({
      where,
      include: {
        template: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Get email delivery rate by day
   */
  async getDeliveryRateByDay(days: number = 7): Promise<
    {
      date: string;
      sent: number;
      failed: number;
      total: number;
      rate: number;
    }[]
  > {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const logs = await this.model.findMany({
      where: {
        createdAt: { gte: startDate },
      },
      select: {
        createdAt: true,
        status: true,
      },
    });

    // Group by date
    const dailyStats: Record<
      string,
      { sent: number; failed: number; total: number }
    > = {};

    logs.forEach((log) => {
      const date = log.createdAt.toISOString().split("T")[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { sent: 0, failed: 0, total: 0 };
      }

      dailyStats[date].total++;
      if (log.status === EmailStatus.SENT) {
        dailyStats[date].sent++;
      } else if (
        log.status === EmailStatus.FAILED ||
        log.status === EmailStatus.BOUNCED
      ) {
        dailyStats[date].failed++;
      }
    });

    return Object.entries(dailyStats).map(([date, stats]) => ({
      date,
      sent: stats.sent,
      failed: stats.failed,
      total: stats.total,
      rate: stats.total > 0 ? (stats.sent / stats.total) * 100 : 0,
    }));
  }
}
