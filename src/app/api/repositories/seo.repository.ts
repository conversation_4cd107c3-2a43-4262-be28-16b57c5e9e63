/**
 * SEO Repository
 * <PERSON><PERSON><PERSON>n lý các thao tác database cho SEO models
 */

import { SEOSettings, PageSEO, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";
import {
  SEOSettingsDTO,
  PageSEODTO,
  CreateSEOSettingsDTO,
  UpdateSEOSettingsDTO,
  CreatePageSEODTO,
  UpdatePageSEODTO,
  SEOSearchFiltersDTO,
  transformSEOSettingsToDTO,
  transformPageSEOToDTO,
  transformCreateSEOSettingsDTOToPrisma,
  transformUpdateSEOSettingsDTOToPrisma,
  transformCreatePageSEODTOToPrisma,
  transformUpdatePageSEODTOToPrisma,
} from "../../dto";

export class SEORepository extends BaseRepository<
  SEOSettings,
  Prisma.SEOSettingsCreateInput,
  Prisma.SEOSettingsUpdateInput
> {
  constructor() {
    super("sEOSettings");
  }

  // SEO Settings Methods

  /**
   * Lấy global SEO settings (chỉ có 1 record)
   */
  async getGlobalSettings(): Promise<SEOSettingsDTO | null> {
    const settings = await this.model.findFirst();
    return settings ? transformSEOSettingsToDTO(settings) : null;
  }

  /**
   * Tạo hoặc cập nhật global SEO settings
   */
  async upsertGlobalSettings(data: CreateSEOSettingsDTO): Promise<SEOSettingsDTO> {
    const existingSettings = await this.model.findFirst();
    
    if (existingSettings) {
      const prismaData = transformUpdateSEOSettingsDTOToPrisma(data);
      const updated = await this.update(existingSettings.id, prismaData);
      return transformSEOSettingsToDTO(updated);
    } else {
      const prismaData = transformCreateSEOSettingsDTOToPrisma(data);
      const created = await this.create(prismaData);
      return transformSEOSettingsToDTO(created);
    }
  }

  /**
   * Cập nhật global SEO settings
   */
  async updateGlobalSettings(data: UpdateSEOSettingsDTO): Promise<SEOSettingsDTO> {
    const existingSettings = await this.model.findFirst();
    
    if (!existingSettings) {
      throw new NotFoundError("SEO Settings", "global");
    }

    const prismaData = transformUpdateSEOSettingsDTOToPrisma(data);
    const updated = await this.update(existingSettings.id, prismaData);
    return transformSEOSettingsToDTO(updated);
  }

  // Page SEO Methods

  /**
   * Tạo Page SEO
   */
  async createPageSEO(data: CreatePageSEODTO): Promise<PageSEODTO> {
    // Kiểm tra path đã tồn tại
    const existingPageSEO = await this.prisma.pageSEO.findUnique({
      where: { path: data.path },
    });

    if (existingPageSEO) {
      throw new ConflictError(`Page SEO for path '${data.path}' already exists`);
    }

    const prismaData = transformCreatePageSEODTOToPrisma(data);
    const pageSEO = await this.prisma.pageSEO.create({ data: prismaData });
    return transformPageSEOToDTO(pageSEO);
  }

  /**
   * Cập nhật Page SEO
   */
  async updatePageSEO(id: string, data: UpdatePageSEODTO): Promise<PageSEODTO> {
    const pageSEO = await this.prisma.pageSEO.findUnique({
      where: { id },
    });

    if (!pageSEO) {
      throw new NotFoundError("PageSEO", id);
    }

    const prismaData = transformUpdatePageSEODTOToPrisma(data);
    const updated = await this.prisma.pageSEO.update({
      where: { id },
      data: prismaData,
    });

    return transformPageSEOToDTO(updated);
  }

  /**
   * Xóa Page SEO
   */
  async deletePageSEO(id: string): Promise<PageSEODTO> {
    const pageSEO = await this.prisma.pageSEO.findUnique({
      where: { id },
    });

    if (!pageSEO) {
      throw new NotFoundError("PageSEO", id);
    }

    const deleted = await this.prisma.pageSEO.delete({
      where: { id },
    });

    return transformPageSEOToDTO(deleted);
  }

  /**
   * Lấy Page SEO theo ID
   */
  async getPageSEOById(id: string): Promise<PageSEODTO | null> {
    const pageSEO = await this.prisma.pageSEO.findUnique({
      where: { id },
    });

    return pageSEO ? transformPageSEOToDTO(pageSEO) : null;
  }

  /**
   * Lấy Page SEO theo path
   */
  async getPageSEOByPath(path: string): Promise<PageSEODTO | null> {
    const pageSEO = await this.prisma.pageSEO.findUnique({
      where: { path },
    });

    return pageSEO ? transformPageSEOToDTO(pageSEO) : null;
  }

  /**
   * Tìm kiếm Page SEOs với filter
   */
  async searchPageSEOs(options: SEOSearchFiltersDTO) {
    const { 
      search, 
      path, 
      isActive, 
      noindex, 
      page = 1, 
      limit = 20, 
      sortBy = "path", 
      sortOrder = "asc" 
    } = options;

    const where: Prisma.PageSEOWhereInput = {};

    if (search) {
      where.OR = [
        { path: { contains: search, mode: "insensitive" } },
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (path) {
      where.path = { contains: path, mode: "insensitive" };
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    if (typeof noindex === "boolean") {
      where.noindex = noindex;
    }

    const result = await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { [sortBy]: sortOrder },
    });

    return {
      ...result,
      data: result.data.map(pageSEO => transformPageSEOToDTO(pageSEO as PageSEO)),
    };
  }

  /**
   * Lấy tất cả active Page SEOs
   */
  async getActivePageSEOs(): Promise<PageSEODTO[]> {
    const pageSEOs = await this.prisma.pageSEO.findMany({
      where: { isActive: true },
      orderBy: { path: "asc" },
    });

    return pageSEOs.map(pageSEO => transformPageSEOToDTO(pageSEO));
  }

  /**
   * Lấy Page SEOs cho sitemap
   */
  async getPageSEOsForSitemap(): Promise<PageSEODTO[]> {
    const pageSEOs = await this.prisma.pageSEO.findMany({
      where: { 
        isActive: true,
        noindex: false,
      },
      orderBy: { priority: "desc" },
    });

    return pageSEOs.map(pageSEO => transformPageSEOToDTO(pageSEO));
  }

  /**
   * Bulk update Page SEOs
   */
  async bulkUpdatePageSEOs(
    ids: string[],
    data: Partial<UpdatePageSEODTO>
  ): Promise<void> {
    const prismaData = transformUpdatePageSEODTOToPrisma(data);
    
    await this.prisma.pageSEO.updateMany({
      where: { id: { in: ids } },
      data: prismaData,
    });
  }

  /**
   * Bulk delete Page SEOs
   */
  async bulkDeletePageSEOs(ids: string[]): Promise<void> {
    await this.prisma.pageSEO.deleteMany({
      where: { id: { in: ids } },
    });
  }

  /**
   * Lấy thống kê SEO
   */
  async getSEOStats() {
    const [
      totalPages,
      activePages,
      inactivePages,
      noindexPages,
      nofollowPages,
      pagesWithoutTitle,
      pagesWithoutDescription,
    ] = await Promise.all([
      this.prisma.pageSEO.count(),
      this.prisma.pageSEO.count({ where: { isActive: true } }),
      this.prisma.pageSEO.count({ where: { isActive: false } }),
      this.prisma.pageSEO.count({ where: { noindex: true } }),
      this.prisma.pageSEO.count({ where: { nofollow: true } }),
      this.prisma.pageSEO.count({ where: { title: null } }),
      this.prisma.pageSEO.count({ where: { description: null } }),
    ]);

    return {
      totalPages,
      activePages,
      inactivePages,
      noindexPages,
      nofollowPages,
      pagesWithoutTitle,
      pagesWithoutDescription,
    };
  }

  /**
   * Tìm Page SEOs có vấn đề SEO
   */
  async findSEOIssues(): Promise<{
    missingTitles: PageSEODTO[];
    missingDescriptions: PageSEODTO[];
    longTitles: PageSEODTO[];
    longDescriptions: PageSEODTO[];
    duplicateTitles: PageSEODTO[];
  }> {
    const [
      missingTitles,
      missingDescriptions,
      longTitles,
      longDescriptions,
    ] = await Promise.all([
      this.prisma.pageSEO.findMany({
        where: { title: null, isActive: true },
      }),
      this.prisma.pageSEO.findMany({
        where: { description: null, isActive: true },
      }),
      this.prisma.pageSEO.findMany({
        where: { 
          title: { not: null },
          isActive: true,
        },
      }).then(pages => pages.filter(p => p.title && p.title.length > 60)),
      this.prisma.pageSEO.findMany({
        where: { 
          description: { not: null },
          isActive: true,
        },
      }).then(pages => pages.filter(p => p.description && p.description.length > 160)),
    ]);

    // Find duplicate titles
    const allTitles = await this.prisma.pageSEO.findMany({
      where: { 
        title: { not: null },
        isActive: true,
      },
      select: { id: true, path: true, title: true },
    });

    const titleCounts = new Map<string, PageSEO[]>();
    allTitles.forEach(page => {
      if (page.title) {
        if (!titleCounts.has(page.title)) {
          titleCounts.set(page.title, []);
        }
        titleCounts.get(page.title)!.push(page as PageSEO);
      }
    });

    const duplicateTitles: PageSEO[] = [];
    titleCounts.forEach((pages, title) => {
      if (pages.length > 1) {
        duplicateTitles.push(...pages);
      }
    });

    return {
      missingTitles: missingTitles.map(transformPageSEOToDTO),
      missingDescriptions: missingDescriptions.map(transformPageSEOToDTO),
      longTitles: longTitles.map(transformPageSEOToDTO),
      longDescriptions: longDescriptions.map(transformPageSEOToDTO),
      duplicateTitles: duplicateTitles.map(transformPageSEOToDTO),
    };
  }

  /**
   * Validate path uniqueness
   */
  async isPathUnique(path: string, excludeId?: string): Promise<boolean> {
    const where: Prisma.PageSEOWhereInput = { path };
    
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const existing = await this.prisma.pageSEO.findFirst({ where });
    return !existing;
  }
}