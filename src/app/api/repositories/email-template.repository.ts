/**
 * Email Template Repository
 * Data access layer cho Email Template
 */

import { EmailTemplate, EmailTemplateType, Prisma } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import {
  EmailTemplateEntity,
  EmailTemplateWithRelations,
  CreateEmailTemplateData,
  UpdateEmailTemplateData,
  EmailTemplateSearchOptions,
} from "@/app/models";

type EmailTemplateCreateInput = Prisma.EmailTemplateCreateInput;
type EmailTemplateUpdateInput = Prisma.EmailTemplateUpdateInput;
type EmailTemplateWithCount = EmailTemplate & {
  _count: {
    emailLogs: number;
  };
};

export class EmailTemplateRepository extends BaseRepository<
  EmailTemplate,
  EmailTemplateCreateInput,
  EmailTemplateUpdateInput
> {
  constructor() {
    super("emailTemplate");
  }

  /**
   * Find template by type and default status
   */
  async findByTypeAndDefault(
    type: EmailTemplateType,
    isDefault: boolean = true
  ): Promise<EmailTemplateEntity | null> {
    const template = await this.model.findFirst({
      where: {
        type,
        isDefault,
        isActive: true,
      },
    });

    return template;
  }

  /**
   * Find all templates by type
   */
  async findByType(type: EmailTemplateType): Promise<EmailTemplateEntity[]> {
    const templates = await this.model.findMany({
      where: { type },
      orderBy: [
        { isDefault: "desc" },
        { isActive: "desc" },
        { createdAt: "desc" },
      ],
    });

    return templates;
  }

  /**
   * Find default template for type
   */
  async findDefaultByType(
    type: EmailTemplateType
  ): Promise<EmailTemplateEntity | null> {
    return await this.findByTypeAndDefault(type, true);
  }

  /**
   * Search templates with pagination
   */
  async search(options: EmailTemplateSearchOptions) {
    const {
      page = 1,
      limit = 20,
      type,
      isActive,
      isDefault,
      createdBy,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.EmailTemplateWhereInput = {};

    if (type) {
      where.type = type;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (isDefault !== undefined) {
      where.isDefault = isDefault;
    }

    if (createdBy) {
      where.createdBy = createdBy;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    const orderBy: Prisma.EmailTemplateOrderByWithRelationInput =
      sortBy === "createdAt"
        ? { createdAt: sortOrder }
        : sortBy === "updatedAt"
          ? { updatedAt: sortOrder }
          : sortBy === "name"
            ? { name: sortOrder }
            : sortBy === "subject"
              ? { subject: sortOrder }
              : sortBy === "type"
                ? { type: sortOrder }
                : { createdAt: sortOrder };

    return await this.findWithPagination({
      where,
      orderBy,
      include: {
        _count: {
          select: {
            emailLogs: true,
          },
        },
      },
      page,
      limit,
    });
  }

  /**
   * Find templates with email log count
   */
  async findWithCount(): Promise<EmailTemplateWithCount[]> {
    return await this.model.findMany({
      include: {
        _count: {
          select: {
            emailLogs: true,
          },
        },
      },
      orderBy: [
        { isDefault: "desc" },
        { isActive: "desc" },
        { createdAt: "desc" },
      ],
    });
  }

  /**
   * Set template as default for its type
   */
  async setAsDefault(id: string): Promise<EmailTemplateEntity> {
    // First, get the template to know its type
    const template = await this.findById(id);
    if (!template) {
      throw new Error("Template not found");
    }

    // Use transaction to ensure consistency
    return await this.prisma.$transaction(async (tx) => {
      // Remove default status from other templates of the same type
      await tx.emailTemplate.updateMany({
        where: {
          type: template.type,
          isDefault: true,
          id: { not: id },
        },
        data: {
          isDefault: false,
        },
      });

      // Set this template as default
      const updatedTemplate = await tx.emailTemplate.update({
        where: { id },
        data: {
          isDefault: true,
          isActive: true, // Ensure default template is active
        },
      });

      return updatedTemplate;
    });
  }

  /**
   * Create template and handle default logic
   */
  async createTemplate(
    data: CreateEmailTemplateData
  ): Promise<EmailTemplateEntity> {
    return await this.prisma.$transaction(async (tx) => {
      // If this is set as default, remove default from others of same type
      if (data.isDefault) {
        await tx.emailTemplate.updateMany({
          where: {
            type: data.type,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Create the new template
      const template = await tx.emailTemplate.create({
        data: {
          name: data.name,
          subject: data.subject,
          content: data.content,
          type: data.type,
          variables: (data.variables || {}) as Prisma.InputJsonValue,
          isActive: data.isActive ?? true,
          isDefault: data.isDefault ?? false,
          createdBy: data.createdBy,
        },
      });

      return template;
    });
  }

  /**
   * Update template and handle default logic
   */
  async updateTemplate(
    id: string,
    data: UpdateEmailTemplateData
  ): Promise<EmailTemplateEntity> {
    return await this.prisma.$transaction(async (tx) => {
      // If setting as default, remove default from others of same type
      if (data.isDefault) {
        const currentTemplate = await tx.emailTemplate.findUnique({
          where: { id },
          select: { type: true },
        });

        if (currentTemplate) {
          await tx.emailTemplate.updateMany({
            where: {
              type: currentTemplate.type,
              isDefault: true,
              id: { not: id },
            },
            data: {
              isDefault: false,
            },
          });
        }
      }

      // Update the template
      const template = await tx.emailTemplate.update({
        where: { id },
        data: {
          name: data.name,
          subject: data.subject,
          content: data.content,
          type: data.type,
          variables: data.variables as Prisma.InputJsonValue,
          isActive: data.isActive,
          isDefault: data.isDefault,
        },
      });

      return template;
    });
  }

  /**
   * Get templates by usage count
   */
  async findMostUsed(limit: number = 5): Promise<EmailTemplateWithCount[]> {
    return await this.model.findMany({
      include: {
        _count: {
          select: {
            emailLogs: true,
          },
        },
      },
      orderBy: {
        emailLogs: {
          _count: "desc",
        },
      },
      take: limit,
    });
  }

  /**
   * Get templates by type with counts
   */
  async getTemplatesByType(): Promise<Record<EmailTemplateType, number>> {
    const templates = await this.model.findMany({
      select: {
        type: true,
      },
    });

    const templatesByType: Record<string, number> = {};
    templates.forEach((template) => {
      const type = template.type;
      templatesByType[type] = (templatesByType[type] || 0) + 1;
    });

    return templatesByType as Record<EmailTemplateType, number>;
  }

  /**
   * Get recently created templates
   */
  async findRecentlyCreated(limit: number = 5): Promise<EmailTemplateEntity[]> {
    return await this.model.findMany({
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });
  }

  /**
   * Bulk activate/deactivate templates
   */
  async bulkUpdateStatus(ids: string[], isActive: boolean): Promise<number> {
    const result = await this.model.updateMany({
      where: {
        id: { in: ids },
      },
      data: {
        isActive,
      },
    });

    return result.count;
  }

  /**
   * Bulk delete templates
   */
  async bulkDelete(ids: string[]): Promise<number> {
    // First check if any of these templates are default
    const defaultTemplates = await this.model.findMany({
      where: {
        id: { in: ids },
        isDefault: true,
      },
      select: { id: true, type: true },
    });

    if (defaultTemplates.length > 0) {
      throw new Error(
        `Cannot delete default templates: ${defaultTemplates.map((t) => t.type).join(", ")}`
      );
    }

    const result = await this.model.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    return result.count;
  }

  /**
   * Check if template name exists for type
   */
  async isNameExistsForType(
    name: string,
    type: EmailTemplateType,
    excludeId?: string
  ): Promise<boolean> {
    const where: Prisma.EmailTemplateWhereInput = {
      name,
      type,
    };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const count = await this.model.count({ where });
    return count > 0;
  }
}
