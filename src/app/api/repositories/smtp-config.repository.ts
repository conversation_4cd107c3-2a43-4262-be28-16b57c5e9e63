/**
 * SMTP Config Repository
 * Data access layer cho SMTP Configuration
 */

import { SMTPConfig, Prisma } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import {
  SMTPConfigEntity,
  CreateSMTPConfigData,
  UpdateSMTPConfigData,
  SMTPConfigSearchOptions,
  SMTPConfigBusinessRules,
} from "@/app/models";

type SMTPConfigCreateInput = Prisma.SMTPConfigCreateInput;
type SMTPConfigUpdateInput = Prisma.SMTPConfigUpdateInput;

export class SMTPConfigRepository extends BaseRepository<
  SMTPConfig,
  SMTPConfigCreateInput,
  SMTPConfigUpdateInput
> {
  constructor() {
    super("sMTPConfig");
  }

  /**
   * Find default SMTP config
   */
  async findDefault(): Promise<SMTPConfigEntity | null> {
    const config = await this.model.findFirst({
      where: {
        isDefault: true,
        isActive: true,
      },
    });

    return config;
  }

  /**
   * Find active SMTP configs
   */
  async findActive(): Promise<SMTPConfigEntity[]> {
    const configs = await this.model.findMany({
      where: { isActive: true },
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });

    return configs;
  }

  /**
   * Search SMTP configs with pagination
   */
  async search(options: SMTPConfigSearchOptions) {
    const {
      page = 1,
      limit = 20,
      isActive,
      isDefault,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.SMTPConfigWhereInput = {};

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (isDefault !== undefined) {
      where.isDefault = isDefault;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { host: { contains: search, mode: "insensitive" } },
        { fromEmail: { contains: search, mode: "insensitive" } },
        { fromName: { contains: search, mode: "insensitive" } },
      ];
    }

    const orderBy: Prisma.SMTPConfigOrderByWithRelationInput =
      sortBy === "createdAt"
        ? { createdAt: sortOrder }
        : sortBy === "updatedAt"
          ? { updatedAt: sortOrder }
          : sortBy === "name"
            ? { name: sortOrder }
            : sortBy === "host"
              ? { host: sortOrder }
              : { createdAt: sortOrder };

    return await this.findWithPagination({
      where,
      orderBy,
      page,
      limit,
    });
  }

  /**
   * Create SMTP config and handle default logic
   */
  async createConfig(data: CreateSMTPConfigData): Promise<SMTPConfigEntity> {
    return await this.prisma.$transaction(async (tx) => {
      // If this is set as default, remove default from others
      if (data.isDefault) {
        await tx.sMTPConfig.updateMany({
          where: { isDefault: true },
          data: { isDefault: false },
        });
      }

      // Encrypt password before storing
      const encryptedPassword = SMTPConfigBusinessRules.encryptPassword(
        data.password
      );

      // Create the new config
      const config = await tx.sMTPConfig.create({
        data: {
          name: data.name,
          host: data.host,
          port: data.port,
          secure: data.secure ?? true,
          username: data.username,
          password: encryptedPassword,
          fromName: data.fromName,
          fromEmail: data.fromEmail,
          isActive: data.isActive ?? false,
          isDefault: data.isDefault ?? false,
        },
      });

      return config;
    });
  }

  /**
   * Update SMTP config and handle default logic
   */
  async updateConfig(
    id: string,
    data: UpdateSMTPConfigData
  ): Promise<SMTPConfigEntity> {
    return await this.prisma.$transaction(async (tx) => {
      // If setting as default, remove default from others
      if (data.isDefault) {
        await tx.sMTPConfig.updateMany({
          where: {
            isDefault: true,
            id: { not: id },
          },
          data: { isDefault: false },
        });
      }

      // Prepare update data
      const updateData: Prisma.SMTPConfigUpdateInput = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.host !== undefined) updateData.host = data.host;
      if (data.port !== undefined) updateData.port = data.port;
      if (data.secure !== undefined) updateData.secure = data.secure;
      if (data.username !== undefined) updateData.username = data.username;
      if (data.password !== undefined) {
        updateData.password = SMTPConfigBusinessRules.encryptPassword(
          data.password
        );
      }
      if (data.fromName !== undefined) updateData.fromName = data.fromName;
      if (data.fromEmail !== undefined) updateData.fromEmail = data.fromEmail;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;
      if (data.isDefault !== undefined) updateData.isDefault = data.isDefault;

      // Update the config
      const config = await tx.sMTPConfig.update({
        where: { id },
        data: updateData,
      });

      return config;
    });
  }

  /**
   * Set config as default
   */
  async setAsDefault(id: string): Promise<SMTPConfigEntity> {
    return await this.prisma.$transaction(async (tx) => {
      // Remove default status from other configs
      await tx.sMTPConfig.updateMany({
        where: {
          isDefault: true,
          id: { not: id },
        },
        data: { isDefault: false },
      });

      // Set this config as default and active
      const config = await tx.sMTPConfig.update({
        where: { id },
        data: {
          isDefault: true,
          isActive: true,
        },
      });

      return config;
    });
  }

  /**
   * Get config with decrypted password
   */
  async getConfigWithPassword(id: string): Promise<SMTPConfigEntity | null> {
    const config = await this.findById(id);
    if (!config) {
      return null;
    }

    // Decrypt password for use
    return {
      ...config,
      password: SMTPConfigBusinessRules.decryptPassword(config.password),
    };
  }

  /**
   * Get default config with decrypted password
   */
  async getDefaultConfigWithPassword(): Promise<SMTPConfigEntity | null> {
    const config = await this.findDefault();
    if (!config) {
      return null;
    }

    // Decrypt password for use
    return {
      ...config,
      password: SMTPConfigBusinessRules.decryptPassword(config.password),
    };
  }

  /**
   * Test SMTP connection
   */
  async testConnection(
    id: string
  ): Promise<{ success: boolean; message: string; error?: string }> {
    const config = await this.getConfigWithPassword(id);
    if (!config) {
      return {
        success: false,
        message: "SMTP configuration not found",
        error: "Config not found",
      };
    }

    try {
      // Here you would implement actual SMTP connection test
      // For now, just validate the configuration
      const validation = SMTPConfigBusinessRules.validateConfig({
        name: config.name,
        host: config.host,
        port: config.port,
        secure: config.secure,
        username: config.username,
        password: config.password,
        fromName: config.fromName,
        fromEmail: config.fromEmail,
      });

      if (!validation.valid) {
        return {
          success: false,
          message: "Configuration validation failed",
          error: validation.errors.join(", "),
        };
      }

      // TODO: Implement actual SMTP connection test using nodemailer
      return {
        success: true,
        message: "SMTP connection successful",
      };
    } catch (error) {
      return {
        success: false,
        message: "SMTP connection failed",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Bulk activate/deactivate configs
   */
  async bulkUpdateStatus(ids: string[], isActive: boolean): Promise<number> {
    const result = await this.model.updateMany({
      where: {
        id: { in: ids },
      },
      data: {
        isActive,
        // If deactivating, also remove default status
        ...(isActive === false && { isDefault: false }),
      },
    });

    return result.count;
  }

  /**
   * Bulk delete configs
   */
  async bulkDelete(ids: string[]): Promise<number> {
    // First check if any of these configs are default
    const defaultConfigs = await this.model.findMany({
      where: {
        id: { in: ids },
        isDefault: true,
      },
      select: { id: true, name: true },
    });

    if (defaultConfigs.length > 0) {
      throw new Error(
        `Cannot delete default configurations: ${defaultConfigs.map((c) => c.name).join(", ")}`
      );
    }

    const result = await this.model.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    return result.count;
  }

  /**
   * Check if config name exists
   */
  async isNameExists(name: string, excludeId?: string): Promise<boolean> {
    const where: Prisma.SMTPConfigWhereInput = { name };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const count = await this.model.count({ where });
    return count > 0;
  }

  /**
   * Get configs by provider (based on host)
   */
  async findByProvider(provider: string): Promise<SMTPConfigEntity[]> {
    const providerSettings =
      SMTPConfigBusinessRules.getProviderSettings(provider);
    if (!providerSettings) {
      return [];
    }

    return await this.model.findMany({
      where: {
        host: providerSettings.host,
      },
      orderBy: [
        { isDefault: "desc" },
        { isActive: "desc" },
        { createdAt: "desc" },
      ],
    });
  }

  /**
   * Get recently used configs
   */
  async getRecentlyUsed(limit: number = 5): Promise<SMTPConfigEntity[]> {
    // This would require tracking usage in email logs
    // For now, just return recently created active configs
    return await this.model.findMany({
      where: { isActive: true },
      orderBy: { updatedAt: "desc" },
      take: limit,
    });
  }

  /**
   * Get config statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    hasDefault: boolean;
    byProvider: Record<string, number>;
  }> {
    const [total, active, defaultConfig] = await Promise.all([
      this.model.count(),
      this.model.count({ where: { isActive: true } }),
      this.model.findFirst({ where: { isDefault: true } }),
    ]);

    // Group by provider (based on host)
    const configs = await this.model.findMany({
      select: { host: true },
    });

    const byProvider: Record<string, number> = {};
    configs.forEach((config) => {
      // Try to identify provider by host
      let provider = "other";
      if (config.host.includes("gmail")) provider = "gmail";
      else if (config.host.includes("outlook")) provider = "outlook";
      else if (config.host.includes("sendgrid")) provider = "sendgrid";
      else if (config.host.includes("mailgun")) provider = "mailgun";
      else if (config.host.includes("amazonaws")) provider = "ses";

      byProvider[provider] = (byProvider[provider] || 0) + 1;
    });

    return {
      total,
      active,
      hasDefault: Boolean(defaultConfig),
      byProvider,
    };
  }
}
