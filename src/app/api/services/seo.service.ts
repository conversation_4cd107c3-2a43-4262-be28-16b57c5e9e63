/**
 * SEO Service
 * Business logic cho SEO management
 */

import { BaseService } from "./base.service";
import { Injectable, SERVICE_IDENTIFIERS } from "../di-container";
import { SEORepository } from "../repositories";
import {
  SEOSettingsEntity,
  PageSEOEntity,
  CreateSEOSettingsData,
  UpdateSEOSettingsData,
  CreatePageSEOData,
  UpdatePageSEOData,
  SEOBusinessRules,
} from "../../models/seo.model";
import {
  SEOSettingsDTO,
  PageSEODTO,
  CreateSEOSettingsDTO,
  UpdateSEOSettingsDTO,
  CreatePageSEODTO,
  UpdatePageSEODTO,
  SEOSearchFiltersDTO,
  SEOStatsDTO,
} from "../../dto";
import {
  PaginatedResult,
  NotFoundError,
  ValidationError,
  ConflictError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const SEO_SERVICE = Symbol("SEOService");

export class SEOService extends BaseService {
  private seoRepository: SEORepository;

  constructor() {
    super();
    this.seoRepository = this.getRepository<SEORepository>(
      SERVICE_IDENTIFIERS.SEO_REPOSITORY
    );
  }

  // SEO Settings Methods

  /**
   * Lấy global SEO settings
   */
  async getGlobalSettings(): Promise<SEOSettingsDTO | null> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.getGlobalSettings();
    }, "getGlobalSettings");
  }

  /**
   * Tạo hoặc cập nhật global SEO settings
   */
  async upsertGlobalSettings(
    data: CreateSEOSettingsDTO,
    updatedBy: UserEntity
  ): Promise<SEOSettingsDTO> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can manage SEO settings");
      }

      // Validate SEO settings data
      const validation = SEOBusinessRules.validateSEOSettings(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      const settings = await this.seoRepository.upsertGlobalSettings(data);

      // Log activity
      await this.logActivity("SEO_SETTINGS_UPDATED", updatedBy.id, {
        settingsId: settings.id,
        changes: data,
      });

      return settings;
    }, "upsertGlobalSettings");
  }

  /**
   * Cập nhật global SEO settings
   */
  async updateGlobalSettings(
    data: UpdateSEOSettingsDTO,
    updatedBy: UserEntity
  ): Promise<SEOSettingsDTO> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can update SEO settings");
      }

      // Validate SEO settings data
      const validation = SEOBusinessRules.validateSEOSettings(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      const settings = await this.seoRepository.updateGlobalSettings(data);

      // Log activity
      await this.logActivity("SEO_SETTINGS_UPDATED", updatedBy.id, {
        settingsId: settings.id,
        changes: data,
      });

      return settings;
    }, "updateGlobalSettings");
  }

  // Page SEO Methods

  /**
   * Tạo Page SEO mới
   */
  async createPageSEO(
    data: CreatePageSEODTO,
    createdBy: UserEntity
  ): Promise<PageSEODTO> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ValidationError("Only admins can create page SEO");
      }

      // Validate required fields
      this.validateRequired(data, ["path"]);

      // Validate page SEO data
      const validation = SEOBusinessRules.validatePageSEO(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      const pageSEO = await this.seoRepository.createPageSEO(data);

      // Log activity
      await this.logActivity("PAGE_SEO_CREATED", createdBy.id, {
        pageSEOId: pageSEO.id,
        path: pageSEO.path,
      });

      return pageSEO;
    }, "createPageSEO");
  }

  /**
   * Cập nhật Page SEO
   */
  async updatePageSEO(
    id: string,
    data: UpdatePageSEODTO,
    updatedBy: UserEntity
  ): Promise<PageSEODTO> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can update page SEO");
      }

      // Validate page SEO data
      const validation = SEOBusinessRules.validatePageSEO(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      const pageSEO = await this.seoRepository.updatePageSEO(id, data);

      // Log activity
      await this.logActivity("PAGE_SEO_UPDATED", updatedBy.id, {
        pageSEOId: id,
        changes: data,
      });

      return pageSEO;
    }, "updatePageSEO");
  }

  /**
   * Xóa Page SEO
   */
  async deletePageSEO(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ValidationError("Only admins can delete page SEO");
      }

      const pageSEO = await this.seoRepository.deletePageSEO(id);

      // Log activity
      await this.logActivity("PAGE_SEO_DELETED", deletedBy.id, {
        pageSEOId: id,
        path: pageSEO.path,
      });
    }, "deletePageSEO");
  }

  /**
   * Lấy Page SEO theo ID
   */
  async getPageSEOById(id: string): Promise<PageSEODTO> {
    return this.executeWithErrorHandling(async () => {
      const pageSEO = await this.seoRepository.getPageSEOById(id);
      if (!pageSEO) {
        throw new NotFoundError("PageSEO", id);
      }
      return pageSEO;
    }, "getPageSEOById");
  }

  /**
   * Lấy Page SEO theo path
   */
  async getPageSEOByPath(path: string): Promise<PageSEODTO | null> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.getPageSEOByPath(path);
    }, "getPageSEOByPath");
  }

  /**
   * Tìm kiếm Page SEOs
   */
  async searchPageSEOs(
    filters: SEOSearchFiltersDTO = {}
  ): Promise<PaginatedResult<PageSEODTO>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      return await this.seoRepository.searchPageSEOs(filters);
    }, "searchPageSEOs");
  }

  /**
   * Lấy active Page SEOs
   */
  async getActivePageSEOs(): Promise<PageSEODTO[]> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.getActivePageSEOs();
    }, "getActivePageSEOs");
  }

  /**
   * Lấy Page SEOs cho sitemap
   */
  async getPageSEOsForSitemap(): Promise<PageSEODTO[]> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.getPageSEOsForSitemap();
    }, "getPageSEOsForSitemap");
  }

  /**
   * Bulk update Page SEOs
   */
  async bulkUpdatePageSEOs(
    ids: string[],
    data: Partial<UpdatePageSEODTO>,
    updatedBy: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can bulk update page SEO");
      }

      // Validate page SEO data
      const validation = SEOBusinessRules.validatePageSEO(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      await this.seoRepository.bulkUpdatePageSEOs(ids, data);

      // Log activity
      await this.logActivity("PAGE_SEO_BULK_UPDATED", updatedBy.id, {
        pageIds: ids,
        changes: data,
        count: ids.length,
      });
    }, "bulkUpdatePageSEOs");
  }

  /**
   * Bulk delete Page SEOs
   */
  async bulkDeletePageSEOs(ids: string[], deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ValidationError("Only admins can bulk delete page SEO");
      }

      await this.seoRepository.bulkDeletePageSEOs(ids);

      // Log activity
      await this.logActivity("PAGE_SEO_BULK_DELETED", deletedBy.id, {
        pageIds: ids,
        count: ids.length,
      });
    }, "bulkDeletePageSEOs");
  }

  /**
   * Lấy thống kê SEO
   */
  async getSEOStats(): Promise<SEOStatsDTO> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.getSEOStats();
    }, "getSEOStats");
  }

  /**
   * Tìm các vấn đề SEO
   */
  async findSEOIssues(): Promise<{
    missingTitles: PageSEODTO[];
    missingDescriptions: PageSEODTO[];
    longTitles: PageSEODTO[];
    longDescriptions: PageSEODTO[];
    duplicateTitles: PageSEODTO[];
  }> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.findSEOIssues();
    }, "findSEOIssues");
  }

  /**
   * Generate sitemap XML
   */
  async generateSitemap(): Promise<string> {
    return this.executeWithErrorHandling(async () => {
      const pageSEOs = await this.seoRepository.getPageSEOsForSitemap();
      const globalSettings = await this.seoRepository.getGlobalSettings();

      let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
      sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

      for (const pageSEO of pageSEOs) {
        sitemap += '  <url>\n';
        sitemap += `    <loc>${pageSEO.path}</loc>\n`;
        
        if (pageSEO.priority !== undefined) {
          sitemap += `    <priority>${pageSEO.priority}</priority>\n`;
        }
        
        if (pageSEO.changefreq) {
          sitemap += `    <changefreq>${pageSEO.changefreq}</changefreq>\n`;
        }
        
        sitemap += `    <lastmod>${pageSEO.updatedAt}</lastmod>\n`;
        sitemap += '  </url>\n';
      }

      sitemap += '</urlset>';
      return sitemap;
    }, "generateSitemap");
  }

  /**
   * Generate robots.txt
   */
  async generateRobotsTxt(): Promise<string> {
    return this.executeWithErrorHandling(async () => {
      const globalSettings = await this.seoRepository.getGlobalSettings();
      
      if (globalSettings?.robotsTxt) {
        return globalSettings.robotsTxt;
      }

      // Default robots.txt
      let robotsTxt = "User-agent: *\n";
      robotsTxt += "Allow: /\n\n";
      
      if (globalSettings?.sitemapEnabled) {
        robotsTxt += "Sitemap: /sitemap.xml\n";
      }

      return robotsTxt;
    }, "generateRobotsTxt");
  }

  /**
   * Validate path uniqueness
   */
  async validatePathUniqueness(path: string, excludeId?: string): Promise<boolean> {
    return this.executeWithErrorHandling(async () => {
      return await this.seoRepository.isPathUnique(path, excludeId);
    }, "validatePathUniqueness");
  }

  /**
   * Auto-generate Page SEO từ content
   */
  async autoGeneratePageSEO(
    path: string,
    content: {
      title?: string;
      description?: string;
      keywords?: string[];
    },
    createdBy: UserEntity
  ): Promise<PageSEODTO> {
    return this.executeWithErrorHandling(async () => {
      // Check if page SEO already exists
      const existing = await this.seoRepository.getPageSEOByPath(path);
      if (existing) {
        throw new ConflictError(`Page SEO for path '${path}' already exists`);
      }

      // Auto-generate SEO data
      const seoData: CreatePageSEODTO = {
        path,
        title: content.title,
        description: content.description,
        keywords: content.keywords || [],
        isActive: true,
      };

      return await this.createPageSEO(seoData, createdBy);
    }, "autoGeneratePageSEO");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}