/**
 * Email Template Service
 * Business logic cho Email Template management
 */

import { EmailTemplateType, EmailStatus } from "@prisma/client";
import { EmailTemplateRepository, EmailLogRepository } from "../repositories";
import {
  EmailTemplateEntity,
  EmailTemplateWithRelations,
  CreateEmailTemplateData,
  UpdateEmailTemplateData,
  EmailTemplateSearchOptions,
  EmailTemplateBusinessRules,
} from "@/app/models";
import {
  EmailTemplateDto,
  EmailTemplateWithRelationsDto,
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
  EmailTemplateSearchDto,
  EmailTemplateStatsDto,
  EmailTemplatePreviewDto,
  EmailTemplatePreviewResultDto,
  EmailTemplateValidationResultDto,
  BulkEmailTemplateOperationDto,
  EmailTemplateHelpers,
  EmailTemplateTransform,
  EmailMarketingTransformUtils,
} from "@/app/dto";

export class EmailTemplateService {
  constructor(
    private emailTemplateRepository: EmailTemplateRepository,
    private emailLogRepository: EmailLogRepository
  ) {}

  /**
   * Get all templates with pagination
   */
  async getTemplates(searchDto: EmailTemplateSearchDto) {
    const searchOptions: EmailTemplateSearchOptions = {
      page: searchDto.page,
      limit: searchDto.limit,
      type: searchDto.type,
      isActive: searchDto.isActive,
      isDefault: searchDto.isDefault,
      createdBy: searchDto.createdBy,
      search: searchDto.search,
      sortBy: searchDto.sortBy,
      sortOrder: searchDto.sortOrder,
    };

    const result = await this.emailTemplateRepository.search(searchOptions);
    return EmailMarketingTransformUtils.transformEmailTemplatePaginationResult(result);
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string): Promise<EmailTemplateDto | null> {
    const template = await this.emailTemplateRepository.findById(id);
    if (!template) {
      return null;
    }

    return EmailTemplateTransform.toDto(template);
  }

  /**
   * Get template by type and default status
   */
  async getTemplateByType(
    type: EmailTemplateType,
    isDefault: boolean = true
  ): Promise<EmailTemplateDto | null> {
    const template = await this.emailTemplateRepository.findByTypeAndDefault(type, isDefault);
    if (!template) {
      return null;
    }

    return EmailTemplateTransform.toDto(template);
  }

  /**
   * Create new template
   */
  async createTemplate(createDto: CreateEmailTemplateDto): Promise<EmailTemplateDto> {
    // Validate business rules
    const validation = this.validateTemplate(createDto);
    if (!validation.valid) {
      throw new Error(`Template validation failed: ${validation.errors.join(", ")}`);
    }

    // Check if name exists for this type
    const nameExists = await this.emailTemplateRepository.isNameExistsForType(
      createDto.name,
      createDto.type
    );
    if (nameExists) {
      throw new Error(`Template name "${createDto.name}" already exists for type ${createDto.type}`);
    }

    const createData: CreateEmailTemplateData = {
      name: createDto.name,
      subject: createDto.subject,
      content: createDto.content,
      type: createDto.type,
      variables: createDto.variables || {},
      isActive: createDto.isActive,
      isDefault: createDto.isDefault,
      createdBy: createDto.createdBy,
    };

    const template = await this.emailTemplateRepository.createTemplate(createData);
    return EmailTemplateTransform.toDto(template);
  }

  /**
   * Update template
   */
  async updateTemplate(id: string, updateDto: UpdateEmailTemplateDto): Promise<EmailTemplateDto> {
    const existingTemplate = await this.emailTemplateRepository.findById(id);
    if (!existingTemplate) {
      throw new Error("Template not found");
    }

    // Validate business rules if content or variables are being updated
    if (updateDto.content || updateDto.variables !== undefined) {
      const validation = this.validateTemplate({
        ...existingTemplate,
        ...updateDto,
      } as CreateEmailTemplateDto);
      
      if (!validation.valid) {
        throw new Error(`Template validation failed: ${validation.errors.join(", ")}`);
      }
    }

    // Check if name exists for this type (if name or type is being updated)
    if (updateDto.name || updateDto.type) {
      const nameToCheck = updateDto.name || existingTemplate.name;
      const typeToCheck = updateDto.type || existingTemplate.type;
      
      const nameExists = await this.emailTemplateRepository.isNameExistsForType(
        nameToCheck,
        typeToCheck,
        id
      );
      if (nameExists) {
        throw new Error(`Template name "${nameToCheck}" already exists for type ${typeToCheck}`);
      }
    }

    const updateData: UpdateEmailTemplateData = {
      name: updateDto.name,
      subject: updateDto.subject,
      content: updateDto.content,
      type: updateDto.type,
      variables: updateDto.variables,
      isActive: updateDto.isActive,
      isDefault: updateDto.isDefault,
    };

    const template = await this.emailTemplateRepository.updateTemplate(id, updateData);
    return EmailTemplateTransform.toDto(template);
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string): Promise<void> {
    const template = await this.emailTemplateRepository.findById(id);
    if (!template) {
      throw new Error("Template not found");
    }

    if (template.isDefault) {
      throw new Error("Cannot delete default template");
    }

    await this.emailTemplateRepository.delete(id);
  }

  /**
   * Set template as default
   */
  async setAsDefault(id: string): Promise<EmailTemplateDto> {
    const template = await this.emailTemplateRepository.setAsDefault(id);
    return EmailTemplateTransform.toDto(template);
  }

  /**
   * Preview template with variables
   */
  async previewTemplate(previewDto: EmailTemplatePreviewDto): Promise<EmailTemplatePreviewResultDto> {
    const template = await this.emailTemplateRepository.findById(previewDto.templateId);
    if (!template) {
      throw new Error("Template not found");
    }

    const variables = previewDto.variables || {};
    
    // Replace variables in subject and content
    const subject = this.replaceVariables(template.subject, variables);
    const content = this.replaceVariables(template.content, variables);

    return {
      subject,
      content,
      format: previewDto.format,
    };
  }

  /**
   * Validate template
   */
  validateTemplate(templateData: CreateEmailTemplateDto): EmailTemplateValidationResultDto {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Use business rules for validation
    const businessValidation = EmailTemplateBusinessRules.validateTemplate({
      name: templateData.name,
      subject: templateData.subject,
      content: templateData.content,
      type: templateData.type,
      variables: templateData.variables || {},
      isActive: templateData.isActive,
      isDefault: templateData.isDefault,
      createdBy: templateData.createdBy,
    });

    if (!businessValidation.valid) {
      errors.push(...businessValidation.errors);
    }

    // Additional validation using helpers
    if (templateData.variables) {
      const variableErrors = EmailTemplateHelpers.validateTemplateVariables(
        templateData.content,
        templateData.variables
      );
      errors.push(...variableErrors);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get template statistics
   */
  async getTemplateStats(): Promise<EmailTemplateStatsDto> {
    const [
      totalTemplates,
      activeTemplates,
      templatesByType,
      mostUsedTemplates,
      recentlyCreated,
    ] = await Promise.all([
      this.emailTemplateRepository.count(),
      this.emailTemplateRepository.count({ isActive: true }),
      this.emailTemplateRepository.getTemplatesByType(),
      this.emailTemplateRepository.findMostUsed(1),
      this.emailTemplateRepository.findRecentlyCreated(5),
    ]);

    const mostUsedTemplate = mostUsedTemplates.length > 0 ? {
      id: mostUsedTemplates[0].id,
      name: mostUsedTemplates[0].name,
      usageCount: mostUsedTemplates[0]._count.emailLogs,
    } : null;

    return {
      totalTemplates,
      activeTemplates,
      templatesByType,
      mostUsedTemplate,
      recentlyCreated: EmailTemplateTransform.toDtoArray(recentlyCreated),
    };
  }

  /**
   * Bulk operations on templates
   */
  async bulkOperation(operationDto: BulkEmailTemplateOperationDto): Promise<{ affected: number }> {
    const { templateIds, operation } = operationDto;

    let affected = 0;

    switch (operation) {
      case "activate":
        affected = await this.emailTemplateRepository.bulkUpdateStatus(templateIds, true);
        break;
      case "deactivate":
        affected = await this.emailTemplateRepository.bulkUpdateStatus(templateIds, false);
        break;
      case "delete":
        affected = await this.emailTemplateRepository.bulkDelete(templateIds);
        break;
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }

    return { affected };
  }

  /**
   * Replace variables in text
   */
  private replaceVariables(text: string, variables: Record<string, any>): string {
    let result = text;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      result = result.replace(regex, String(value));
    }

    return result;
  }
}
