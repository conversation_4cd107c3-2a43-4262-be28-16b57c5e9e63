/**
 * SMTP Config Service
 * Business logic cho SMTP Configuration management
 */

import { SMTPConfigRepository } from "../repositories";
import {
  SMTPConfigEntity,
  CreateSMTPConfigData,
  UpdateSMTPConfigData,
  SMTPConfigSearchOptions,
  SMTPConfigBusinessRules,
} from "@/app/models";
import {
  SMTPConfigDto,
  SMTPConfigSafeDto,
  CreateSMTPConfigDto,
  UpdateSMTPConfigDto,
  SMTPConfigSearchDto,
  SMTPTestDto,
  SMTPTestResultDto,
  SMTPConfigStatsDto,
  BulkSMTPConfigOperationDto,
  SMTPProviderTemplateDto,
  SMTPConfigHelpers,
  SMTPConfigTransform,
  EmailMarketingTransformUtils,
} from "@/app/dto";

export class SMTPConfigService {
  constructor(private smtpConfigRepository: SMTPConfigRepository) {}

  /**
   * Get all SMTP configs with pagination
   */
  async getConfigs(searchDto: SMTPConfigSearchDto) {
    const searchOptions: SMTPConfigSearchOptions = {
      page: searchDto.page,
      limit: searchDto.limit,
      isActive: searchDto.isActive,
      isDefault: searchDto.isDefault,
      search: searchDto.search,
      sortBy: searchDto.sortBy,
      sortOrder: searchDto.sortOrder,
    };

    const result = await this.smtpConfigRepository.search(searchOptions);
    return EmailMarketingTransformUtils.transformSMTPConfigPaginationResult(result);
  }

  /**
   * Get config by ID (safe - without password)
   */
  async getConfigById(id: string): Promise<SMTPConfigSafeDto | null> {
    const config = await this.smtpConfigRepository.findById(id);
    if (!config) {
      return null;
    }

    return SMTPConfigTransform.toSafeDto(config);
  }

  /**
   * Get config with password (for internal use)
   */
  async getConfigWithPassword(id: string): Promise<SMTPConfigDto | null> {
    const config = await this.smtpConfigRepository.getConfigWithPassword(id);
    if (!config) {
      return null;
    }

    return SMTPConfigTransform.toDto(config);
  }

  /**
   * Get default SMTP config
   */
  async getDefaultConfig(): Promise<SMTPConfigSafeDto | null> {
    const config = await this.smtpConfigRepository.findDefault();
    if (!config) {
      return null;
    }

    return SMTPConfigTransform.toSafeDto(config);
  }

  /**
   * Get default config with password (for internal use)
   */
  async getDefaultConfigWithPassword(): Promise<SMTPConfigDto | null> {
    const config = await this.smtpConfigRepository.getDefaultConfigWithPassword();
    if (!config) {
      return null;
    }

    return SMTPConfigTransform.toDto(config);
  }

  /**
   * Get active SMTP configs
   */
  async getActiveConfigs(): Promise<SMTPConfigSafeDto[]> {
    const configs = await this.smtpConfigRepository.findActive();
    return SMTPConfigTransform.toSafeDtoArray(configs);
  }

  /**
   * Create new SMTP config
   */
  async createConfig(createDto: CreateSMTPConfigDto): Promise<SMTPConfigSafeDto> {
    // Validate configuration
    const validation = SMTPConfigHelpers.validateConfig(createDto);
    if (!validation.valid) {
      throw new Error(`SMTP configuration validation failed: ${validation.errors.join(", ")}`);
    }

    // Check if name already exists
    const nameExists = await this.smtpConfigRepository.isNameExists(createDto.name);
    if (nameExists) {
      throw new Error(`SMTP configuration name "${createDto.name}" already exists`);
    }

    const createData: CreateSMTPConfigData = {
      name: createDto.name,
      host: createDto.host,
      port: createDto.port,
      secure: createDto.secure,
      username: createDto.username,
      password: createDto.password,
      fromName: createDto.fromName,
      fromEmail: createDto.fromEmail,
      isActive: createDto.isActive,
      isDefault: createDto.isDefault,
    };

    const config = await this.smtpConfigRepository.createConfig(createData);
    return SMTPConfigTransform.toSafeDto(config);
  }

  /**
   * Update SMTP config
   */
  async updateConfig(id: string, updateDto: UpdateSMTPConfigDto): Promise<SMTPConfigSafeDto> {
    const existingConfig = await this.smtpConfigRepository.findById(id);
    if (!existingConfig) {
      throw new Error("SMTP configuration not found");
    }

    // Validate configuration if relevant fields are being updated
    if (updateDto.host || updateDto.port || updateDto.secure !== undefined) {
      const configToValidate = {
        ...existingConfig,
        ...updateDto,
      } as CreateSMTPConfigDto;

      const validation = SMTPConfigHelpers.validateConfig(configToValidate);
      if (!validation.valid) {
        throw new Error(`SMTP configuration validation failed: ${validation.errors.join(", ")}`);
      }
    }

    // Check if name already exists (if name is being updated)
    if (updateDto.name) {
      const nameExists = await this.smtpConfigRepository.isNameExists(updateDto.name, id);
      if (nameExists) {
        throw new Error(`SMTP configuration name "${updateDto.name}" already exists`);
      }
    }

    const updateData: UpdateSMTPConfigData = {
      name: updateDto.name,
      host: updateDto.host,
      port: updateDto.port,
      secure: updateDto.secure,
      username: updateDto.username,
      password: updateDto.password,
      fromName: updateDto.fromName,
      fromEmail: updateDto.fromEmail,
      isActive: updateDto.isActive,
      isDefault: updateDto.isDefault,
    };

    const config = await this.smtpConfigRepository.updateConfig(id, updateData);
    return SMTPConfigTransform.toSafeDto(config);
  }

  /**
   * Delete SMTP config
   */
  async deleteConfig(id: string): Promise<void> {
    const config = await this.smtpConfigRepository.findById(id);
    if (!config) {
      throw new Error("SMTP configuration not found");
    }

    if (config.isDefault) {
      throw new Error("Cannot delete default SMTP configuration");
    }

    await this.smtpConfigRepository.delete(id);
  }

  /**
   * Set config as default
   */
  async setAsDefault(id: string): Promise<SMTPConfigSafeDto> {
    const config = await this.smtpConfigRepository.setAsDefault(id);
    return SMTPConfigTransform.toSafeDto(config);
  }

  /**
   * Test SMTP connection
   */
  async testConnection(testDto: SMTPTestDto): Promise<SMTPTestResultDto> {
    let configId = testDto.configId;
    
    // If no config ID provided, use default
    if (!configId) {
      const defaultConfig = await this.smtpConfigRepository.findDefault();
      if (!defaultConfig) {
        return {
          success: false,
          message: "No SMTP configuration available",
          error: "No default SMTP configuration found",
        };
      }
      configId = defaultConfig.id;
    }

    const startTime = Date.now();
    const result = await this.smtpConfigRepository.testConnection(configId);
    const responseTime = Date.now() - startTime;

    if (result.success) {
      const config = await this.smtpConfigRepository.findById(configId);
      return {
        ...result,
        responseTime,
        configUsed: config ? {
          id: config.id,
          name: config.name,
          host: config.host,
          port: config.port,
        } : undefined,
      };
    }

    return {
      ...result,
      responseTime,
    };
  }

  /**
   * Get SMTP provider templates
   */
  getProviderTemplates(): SMTPProviderTemplateDto[] {
    return SMTPConfigHelpers.getAllProviderTemplates();
  }

  /**
   * Get provider template by name
   */
  getProviderTemplate(provider: string): SMTPProviderTemplateDto | null {
    return SMTPConfigHelpers.getProviderTemplate(provider);
  }

  /**
   * Get SMTP configuration statistics
   */
  async getStats(): Promise<SMTPConfigStatsDto> {
    const [stats, defaultConfig, recentlyUsed] = await Promise.all([
      this.smtpConfigRepository.getStats(),
      this.smtpConfigRepository.findDefault(),
      this.smtpConfigRepository.getRecentlyUsed(5),
    ]);

    // Test connection status for active configs
    const activeConfigs = await this.smtpConfigRepository.findActive();
    const connectionStatus = await Promise.all(
      activeConfigs.slice(0, 5).map(async (config) => {
        const testResult = await this.smtpConfigRepository.testConnection(config.id);
        return {
          configId: config.id,
          name: config.name,
          connected: testResult.success,
          lastTested: new Date().toISOString(),
          error: testResult.error,
        };
      })
    );

    return {
      totalConfigs: stats.total,
      activeConfigs: stats.active,
      defaultConfig: defaultConfig ? SMTPConfigTransform.toSafeDto(defaultConfig) : null,
      recentlyUsed: SMTPConfigTransform.toSafeDtoArray(recentlyUsed),
      connectionStatus,
    };
  }

  /**
   * Bulk operations on SMTP configs
   */
  async bulkOperation(operationDto: BulkSMTPConfigOperationDto): Promise<{ affected: number }> {
    const { configIds, operation } = operationDto;

    let affected = 0;

    switch (operation) {
      case "activate":
        affected = await this.smtpConfigRepository.bulkUpdateStatus(configIds, true);
        break;
      case "deactivate":
        affected = await this.smtpConfigRepository.bulkUpdateStatus(configIds, false);
        break;
      case "delete":
        affected = await this.smtpConfigRepository.bulkDelete(configIds);
        break;
      case "test":
        // Test all configs and return count of successful tests
        const testResults = await Promise.all(
          configIds.map(id => this.smtpConfigRepository.testConnection(id))
        );
        affected = testResults.filter(result => result.success).length;
        break;
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }

    return { affected };
  }

  /**
   * Get configs by provider
   */
  async getConfigsByProvider(provider: string): Promise<SMTPConfigSafeDto[]> {
    const configs = await this.smtpConfigRepository.findByProvider(provider);
    return SMTPConfigTransform.toSafeDtoArray(configs);
  }

  /**
   * Validate SMTP configuration
   */
  validateConfig(configData: CreateSMTPConfigDto): { valid: boolean; errors: string[] } {
    return SMTPConfigHelpers.validateConfig(configData);
  }

  /**
   * Get recommended port settings
   */
  getRecommendedPorts(): { port: number; description: string; secure: boolean }[] {
    return SMTPConfigHelpers.getRecommendedPorts();
  }

  /**
   * Check if configuration is complete
   */
  isConfigComplete(config: Partial<CreateSMTPConfigDto>): boolean {
    return SMTPConfigHelpers.isConfigComplete(config);
  }
}
