/**
 * Email Log Service
 * Business logic cho Email Log management
 */

import { EmailStatus } from "@prisma/client";
import { EmailLogRepository, EmailTemplateRepository } from "../repositories";
import {
  EmailLogEntity,
  EmailLogWithRelations,
  CreateEmailLogData,
  UpdateEmailLogData,
  EmailLogSearchOptions,
  EmailLogStats,
} from "@/app/models";
import {
  EmailLogDto,
  EmailLogWithRelationsDto,
  CreateEmailLogDto,
  UpdateEmailLogDto,
  EmailLogSearchDto,
  EmailLogStatsDto,
  EmailLogRetryDto,
  EmailLogExportDto,
  EmailLogHelpers,
  EmailLogTransform,
  EmailMarketingTransformUtils,
} from "@/app/dto";

export class EmailLogService {
  constructor(
    private emailLogRepository: EmailLogRepository,
    private emailTemplateRepository: EmailTemplateRepository
  ) {}

  /**
   * Get all logs with pagination
   */
  async getLogs(searchDto: EmailLogSearchDto) {
    const searchOptions: EmailLogSearchOptions = {
      page: searchDto.page,
      limit: searchDto.limit,
      templateId: searchDto.templateId,
      recipient: searchDto.recipient,
      status: searchDto.status,
      dateFrom: searchDto.dateFrom ? new Date(searchDto.dateFrom) : undefined,
      dateTo: searchDto.dateTo ? new Date(searchDto.dateTo) : undefined,
      search: searchDto.search,
      sortBy: searchDto.sortBy,
      sortOrder: searchDto.sortOrder,
    };

    const result = await this.emailLogRepository.search(searchOptions);
    return EmailMarketingTransformUtils.transformEmailLogPaginationResult(result);
  }

  /**
   * Get log by ID
   */
  async getLogById(id: string): Promise<EmailLogDto | null> {
    const log = await this.emailLogRepository.findById(id);
    if (!log) {
      return null;
    }

    return EmailLogTransform.toDto(log);
  }

  /**
   * Get logs by template ID
   */
  async getLogsByTemplateId(templateId: string): Promise<EmailLogDto[]> {
    const logs = await this.emailLogRepository.findByTemplateId(templateId);
    return EmailLogTransform.toDtoArray(logs);
  }

  /**
   * Get logs by recipient
   */
  async getLogsByRecipient(recipient: string): Promise<EmailLogDto[]> {
    const logs = await this.emailLogRepository.findByRecipient(recipient);
    return EmailLogTransform.toDtoArray(logs);
  }

  /**
   * Get logs by status
   */
  async getLogsByStatus(status: EmailStatus): Promise<EmailLogDto[]> {
    const logs = await this.emailLogRepository.findByStatus(status);
    return EmailLogTransform.toDtoArray(logs);
  }

  /**
   * Create new log
   */
  async createLog(createDto: CreateEmailLogDto): Promise<EmailLogDto> {
    // Validate template exists if templateId is provided
    if (createDto.templateId) {
      const template = await this.emailTemplateRepository.findById(createDto.templateId);
      if (!template) {
        throw new Error("Template not found");
      }
    }

    const createData: CreateEmailLogData = {
      templateId: createDto.templateId,
      recipient: createDto.recipient,
      subject: createDto.subject,
      content: createDto.content,
      status: createDto.status || EmailStatus.PENDING,
      error: createDto.error,
      sentAt: createDto.sentAt ? new Date(createDto.sentAt) : undefined,
    };

    const log = await this.emailLogRepository.createLog(createData);
    return EmailLogTransform.toDto(log);
  }

  /**
   * Update log status
   */
  async updateLogStatus(
    id: string,
    status: EmailStatus,
    error?: string,
    sentAt?: string
  ): Promise<EmailLogDto> {
    const log = await this.emailLogRepository.updateStatus(
      id,
      status,
      error,
      sentAt ? new Date(sentAt) : undefined
    );
    return EmailLogTransform.toDto(log);
  }

  /**
   * Mark log as sent
   */
  async markAsSent(id: string): Promise<EmailLogDto> {
    const log = await this.emailLogRepository.markAsSent(id);
    return EmailLogTransform.toDto(log);
  }

  /**
   * Mark log as failed
   */
  async markAsFailed(id: string, error: string): Promise<EmailLogDto> {
    const log = await this.emailLogRepository.markAsFailed(id, error);
    return EmailLogTransform.toDto(log);
  }

  /**
   * Mark log as bounced
   */
  async markAsBounced(id: string, error?: string): Promise<EmailLogDto> {
    const log = await this.emailLogRepository.markAsBounced(id, error);
    return EmailLogTransform.toDto(log);
  }

  /**
   * Get email statistics
   */
  async getStats(dateFrom?: string, dateTo?: string): Promise<EmailLogStatsDto> {
    const stats = await this.emailLogRepository.getStats(
      dateFrom ? new Date(dateFrom) : undefined,
      dateTo ? new Date(dateTo) : undefined
    );

    const [recentActivity, topFailureReasons] = await Promise.all([
      this.emailLogRepository.getRecentActivity(10),
      this.emailLogRepository.getTopFailureReasons(5),
    ]);

    return {
      total: stats.total,
      sent: stats.sent,
      failed: stats.failed,
      pending: stats.pending,
      bounced: stats.bounced,
      successRate: stats.successRate,
      failureRate: stats.failureRate,
      recentActivity: EmailLogTransform.toDtoArray(recentActivity),
      topFailureReasons,
    };
  }

  /**
   * Retry failed emails
   */
  async retryEmails(retryDto: EmailLogRetryDto): Promise<{ affected: number }> {
    const affected = await this.emailLogRepository.bulkRetry(retryDto.logIds);
    return { affected };
  }

  /**
   * Get logs for export
   */
  async getLogsForExport(exportDto: EmailLogExportDto): Promise<EmailLogWithRelationsDto[]> {
    const logs = await this.emailLogRepository.getLogsForExport({
      dateFrom: exportDto.dateFrom ? new Date(exportDto.dateFrom) : undefined,
      dateTo: exportDto.dateTo ? new Date(exportDto.dateTo) : undefined,
      status: exportDto.status,
      templateId: exportDto.templateId,
    });

    return EmailLogTransform.toWithRelationsDtoArray(
      logs.map(log => EmailLogTransform.fromPrismaWithRelations(log))
    );
  }

  /**
   * Clean old logs
   */
  async cleanOldLogs(olderThanDays: number): Promise<{ deleted: number }> {
    const deleted = await this.emailLogRepository.cleanOldLogs(olderThanDays);
    return { deleted };
  }

  /**
   * Get delivery rate by day
   */
  async getDeliveryRateByDay(days: number = 7): Promise<{
    date: string;
    sent: number;
    failed: number;
    total: number;
    rate: number;
  }[]> {
    return await this.emailLogRepository.getDeliveryRateByDay(days);
  }

  /**
   * Get retryable logs
   */
  async getRetryableLogs(): Promise<EmailLogDto[]> {
    const logs = await this.emailLogRepository.findRetryable();
    return EmailLogTransform.toDtoArray(logs);
  }

  /**
   * Export logs to CSV format
   */
  async exportToCSV(exportDto: EmailLogExportDto): Promise<string> {
    const logs = await this.getLogsForExport(exportDto);
    
    const headers = [
      "ID",
      "Template",
      "Recipient",
      "Subject",
      "Status",
      "Error",
      "Sent At",
      "Created At",
    ];

    const rows = logs.map(log => [
      log.id,
      log.template?.name || "N/A",
      log.recipient,
      log.subject,
      EmailLogHelpers.getStatusDisplayName(log.status),
      log.error || "",
      log.sentAt || "",
      log.createdAt,
    ]);

    // Convert to CSV format
    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(",")),
    ].join("\n");

    return csvContent;
  }

  /**
   * Get email performance metrics
   */
  async getPerformanceMetrics(days: number = 30): Promise<{
    totalEmails: number;
    successRate: number;
    averageDeliveryTime: number;
    bounceRate: number;
    topFailureReasons: { error: string; count: number }[];
    dailyStats: {
      date: string;
      sent: number;
      failed: number;
      total: number;
      rate: number;
    }[];
  }> {
    const [stats, topFailureReasons, dailyStats] = await Promise.all([
      this.getStats(),
      this.emailLogRepository.getTopFailureReasons(5),
      this.getDeliveryRateByDay(days),
    ]);

    // Calculate average delivery time (this would need additional tracking in the future)
    const averageDeliveryTime = 0; // Placeholder

    return {
      totalEmails: stats.total,
      successRate: stats.successRate,
      averageDeliveryTime,
      bounceRate: stats.total > 0 ? (stats.bounced / stats.total) * 100 : 0,
      topFailureReasons,
      dailyStats,
    };
  }

  /**
   * Get email trends
   */
  async getEmailTrends(days: number = 30): Promise<{
    period: string;
    totalEmails: number;
    successRate: number;
    growthRate: number;
  }[]> {
    const dailyStats = await this.getDeliveryRateByDay(days);
    
    // Group by week for trends
    const weeklyStats: Record<string, { total: number; sent: number }> = {};
    
    dailyStats.forEach(day => {
      const date = new Date(day.date);
      const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklyStats[weekKey]) {
        weeklyStats[weekKey] = { total: 0, sent: 0 };
      }
      
      weeklyStats[weekKey].total += day.total;
      weeklyStats[weekKey].sent += day.sent;
    });

    const trends = Object.entries(weeklyStats).map(([week, stats], index, array) => {
      const successRate = stats.total > 0 ? (stats.sent / stats.total) * 100 : 0;
      
      // Calculate growth rate compared to previous week
      let growthRate = 0;
      if (index > 0) {
        const prevStats = array[index - 1][1];
        const prevTotal = prevStats.total;
        if (prevTotal > 0) {
          growthRate = ((stats.total - prevTotal) / prevTotal) * 100;
        }
      }

      return {
        period: week,
        totalEmails: stats.total,
        successRate: Math.round(successRate * 100) / 100,
        growthRate: Math.round(growthRate * 100) / 100,
      };
    });

    return trends;
  }
}
