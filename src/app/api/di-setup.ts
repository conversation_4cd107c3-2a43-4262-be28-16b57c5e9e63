/**
 * Dependency Injection Setup
 * Khởi tạo và cấu hình tất cả dependencies cho container
 */

import { container, SERVICE_IDENTIFIERS } from "./di-container";
import { CacheService } from "./services/cache.service";

// Import repositories
import {
  UserRepository,
  AdminUserRepository,
  ProductRepository,
  CategoryRepository,
  BrandRepository,
  OrderRepository,
  CartRepository,
  AddressRepository,
  ReviewRepository,
  WishlistRepository,
  PostRepository,
  PageRepository,
  MediaRepository,
  MenuRepository,
  SEORepository,
  SettingRepository,
  NotificationRepository,
  AuditLogRepository,
  ContactRepository,
  AttributeRepository,
  InventoryRepository,
  PromotionRepository,
  ShippingZoneRepository,
  ShippingMethodRepository,
  EmailTemplateRepository,
  EmailLogRepository,
  SMTPConfigRepository,
} from "./repositories";
import { PaymentGatewayRepository } from "./repositories/payment-gateway.repository";
import { PaymentTransactionRepository } from "./repositories/payment-transaction.repository";

// Import services (classes only, identifiers from service-identifiers.ts)
import { UserService } from "./services/user.service";
import { ProductService } from "./services/product.service";
import { CategoryService } from "./services/category.service";
import { OrderService } from "./services/order.service";
import { CartService } from "./services/cart.service";
import { BrandService } from "./services/brand.service";
import { AddressService } from "./services/address.service";
import { ReviewService } from "./services/review.service";
import { WishlistService } from "./services/wishlist.service";
import { MediaService } from "./services/media.service";
import { MenuService } from "./services/menu.service";
import { SEOService } from "./services/seo.service";
import { AdminUserService } from "./services/admin-user.service";
import { SettingService } from "./services/setting.service";
import { NotificationService } from "./services/notification.service";
import { AuditLogService } from "./services/audit-log.service";
import { PostService } from "./services/post.service";
import { PageService } from "./services/page.service";
import { ContactService } from "./services/contact.service";
import { AttributeService } from "./services/attribute.service";
import { InventoryService } from "./services/inventory.service";
import { PromotionService } from "./services/promotion.service";
import { ShippingService } from "./services/shipping.service";
import { EventService } from "./services/event.service";
import { NotificationHandlersService } from "./services/notification-handlers.service";
import { WebSocketService } from "./services/websocket.service";
import { EmailService } from "./services/email.service";
import { MetricsService } from "./services/metrics.service";
import { RateLimitService } from "./services/rate-limit.service";
import { PaymentGatewayService } from "./services/payment-gateway.service";
import { PaymentTransactionService } from "./services/payment-transaction.service";
import { EmailTemplateService } from "./services/email-template.service";
import { EmailLogService } from "./services/email-log.service";
import { SMTPConfigService } from "./services/smtp-config.service";
import { getPrismaClient } from "@prisma/client/runtime/library";

/**
 * Setup tất cả repositories trong DI container
 */
export function setupRepositories(): void {
  // Core repositories
  container.register(
    SERVICE_IDENTIFIERS.USER_REPOSITORY,
    () => new UserRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.ADMIN_USER_REPOSITORY,
    () => new AdminUserRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY,
    () => new ProductRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY,
    () => new CategoryRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.BRAND_REPOSITORY,
    () => new BrandRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.ORDER_REPOSITORY,
    () => new OrderRepository()
  );

  // E-commerce repositories
  container.register(
    SERVICE_IDENTIFIERS.CART_REPOSITORY,
    () => new CartRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.ADDRESS_REPOSITORY,
    () => new AddressRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.REVIEW_REPOSITORY,
    () => new ReviewRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.WISHLIST_REPOSITORY,
    () => new WishlistRepository()
  );

  // Content repositories
  container.register(
    SERVICE_IDENTIFIERS.POST_REPOSITORY,
    () => new PostRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.PAGE_REPOSITORY,
    () => new PageRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.MEDIA_REPOSITORY,
    () => new MediaRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.MENU_REPOSITORY,
    () => new MenuRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.SEO_REPOSITORY,
    () => new SEORepository()
  );

  // System repositories
  container.register(
    SERVICE_IDENTIFIERS.SETTING_REPOSITORY,
    () => new SettingRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.NOTIFICATION_REPOSITORY,
    () => new NotificationRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY,
    () => new AuditLogRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.CONTACT_REPOSITORY,
    () => new ContactRepository()
  );

  // Advanced repositories
  container.register(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY,
    () => new AttributeRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY,
    () => new InventoryRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.PROMOTION_REPOSITORY,
    () => new PromotionRepository()
  );

  // Shipping repositories
  container.register(
    SERVICE_IDENTIFIERS.SHIPPING_ZONE_REPOSITORY,
    () => new ShippingZoneRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.SHIPPING_METHOD_REPOSITORY,
    () => new ShippingMethodRepository()
  );

  // Email Marketing repositories
  container.register(
    SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_REPOSITORY,
    () => new EmailTemplateRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.EMAIL_LOG_REPOSITORY,
    () => new EmailLogRepository()
  );

  container.register(
    SERVICE_IDENTIFIERS.SMTP_CONFIG_REPOSITORY,
    () => new SMTPConfigRepository()
  );

  // Payment repositories
  container.register(
    SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_REPOSITORY,
    () => new PaymentGatewayRepository(getPrismaClient())
  );

  container.register(
    SERVICE_IDENTIFIERS.PAYMENT_TRANSACTION_REPOSITORY,
    () => new PaymentTransactionRepository(getPrismaClient())
  );

  console.log("All repositories registered successfully");
}

/**
 * Setup tất cả services trong DI container
 */
export function setupServices(): void {
  // Cache Service (register first as other services may depend on it)
  container.register(
    SERVICE_IDENTIFIERS.CACHE_SERVICE,
    () => new CacheService(),
    {
      singleton: true,
    }
  );

  // User Service
  container.register(
    SERVICE_IDENTIFIERS.USER_SERVICE,
    () => new UserService(),
    {
      singleton: true,
    }
  );

  // Product Service
  container.register(
    SERVICE_IDENTIFIERS.PRODUCT_SERVICE,
    () => new ProductService(),
    {
      singleton: true,
    }
  );

  // Category Service
  container.register(
    SERVICE_IDENTIFIERS.CATEGORY_SERVICE,
    () => new CategoryService(),
    {
      singleton: true,
    }
  );

  // Order Service
  container.register(
    SERVICE_IDENTIFIERS.ORDER_SERVICE,
    () => new OrderService(),
    {
      singleton: true,
    }
  );

  // Cart Service
  container.register(
    SERVICE_IDENTIFIERS.CART_SERVICE,
    () => new CartService(),
    {
      singleton: true,
    }
  );

  // Brand Service
  container.register(
    SERVICE_IDENTIFIERS.BRAND_SERVICE,
    () => new BrandService(),
    {
      singleton: true,
    }
  );

  // Address Service
  container.register(
    SERVICE_IDENTIFIERS.ADDRESS_SERVICE,
    () => new AddressService(),
    {
      singleton: true,
    }
  );

  // Shipping Service
  container.register(
    SERVICE_IDENTIFIERS.SHIPPING_SERVICE,
    () => new ShippingService(),
    {
      singleton: true,
    }
  );

  // Review Service
  container.register(
    SERVICE_IDENTIFIERS.REVIEW_SERVICE,
    () =>
      new ReviewService(
        container.resolve(SERVICE_IDENTIFIERS.REVIEW_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.ORDER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Wishlist Service
  container.register(
    SERVICE_IDENTIFIERS.WISHLIST_SERVICE,
    () =>
      new WishlistService(
        container.resolve(SERVICE_IDENTIFIERS.WISHLIST_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Media Service
  container.register(
    SERVICE_IDENTIFIERS.MEDIA_SERVICE,
    () => new MediaService(),
    {
      singleton: true,
    }
  );

  // Menu Service
  container.register(
    SERVICE_IDENTIFIERS.MENU_SERVICE,
    () => new MenuService(),
    {
      singleton: true,
    }
  );

  // SEO Service
  container.register(
    SERVICE_IDENTIFIERS.SEO_SERVICE,
    () => new SEOService(),
    {
      singleton: true,
    }
  );

  // Admin User Service
  container.register(
    SERVICE_IDENTIFIERS.ADMIN_USER_SERVICE,
    () => new AdminUserService(),
    {
      singleton: true,
    }
  );

  // Setting Service
  container.register(
    SERVICE_IDENTIFIERS.SETTING_SERVICE,
    () =>
      new SettingService(
        container.resolve(SERVICE_IDENTIFIERS.SETTING_REPOSITORY)
      ),
    {
      singleton: true,
    }
  );

  // Notification Service
  container.register(
    SERVICE_IDENTIFIERS.NOTIFICATION_SERVICE,
    () =>
      new NotificationService(
        container.resolve(SERVICE_IDENTIFIERS.NOTIFICATION_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    {
      singleton: true,
    }
  );

  // Audit Log Service
  container.register(
    SERVICE_IDENTIFIERS.AUDIT_LOG_SERVICE,
    () =>
      new AuditLogService(
        container.resolve(SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Post Service
  container.register(
    SERVICE_IDENTIFIERS.POST_SERVICE,
    () =>
      new PostService(
        container.resolve(SERVICE_IDENTIFIERS.POST_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY)
      ),
    { singleton: true }
  );

  // Page Service
  container.register(
    SERVICE_IDENTIFIERS.PAGE_SERVICE,
    () =>
      new PageService(container.resolve(SERVICE_IDENTIFIERS.PAGE_REPOSITORY)),
    { singleton: true }
  );

  // Contact Service
  container.register(
    SERVICE_IDENTIFIERS.CONTACT_SERVICE,
    () =>
      new ContactService(
        container.resolve(SERVICE_IDENTIFIERS.CONTACT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Attribute Service
  container.register(
    SERVICE_IDENTIFIERS.ATTRIBUTE_SERVICE,
    () =>
      new AttributeService(
        container.resolve(SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY)
      ),
    { singleton: true }
  );

  // Inventory Service
  container.register(
    SERVICE_IDENTIFIERS.INVENTORY_SERVICE,
    () =>
      new InventoryService(
        container.resolve(SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY)
      ),
    { singleton: true }
  );

  // Promotion Service
  container.register(
    SERVICE_IDENTIFIERS.PROMOTION_SERVICE,
    () =>
      new PromotionService(
        container.resolve(SERVICE_IDENTIFIERS.PROMOTION_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Event and Communication Services
  container.register(
    SERVICE_IDENTIFIERS.EVENT_SERVICE,
    () => EventService.getInstance(),
    {
      singleton: true,
    }
  );

  container.register(
    SERVICE_IDENTIFIERS.NOTIFICATION_HANDLERS_SERVICE,
    () => NotificationHandlersService,
    { singleton: true }
  );

  container.register(
    SERVICE_IDENTIFIERS.WEBSOCKET_SERVICE,
    () => WebSocketService.getInstance(),
    {
      singleton: true,
    }
  );

  container.register(
    SERVICE_IDENTIFIERS.EMAIL_SERVICE,
    () => EmailService.getInstance(),
    {
      singleton: true,
    }
  );

  container.register(
    SERVICE_IDENTIFIERS.METRICS_SERVICE,
    () => MetricsService.getInstance(),
    {
      singleton: true,
    }
  );

  container.register(
    SERVICE_IDENTIFIERS.RATE_LIMIT_SERVICE,
    () => RateLimitService.getInstance(),
    {
      singleton: true,
    }
  );

  // Payment Services
  container.register(
    SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_SERVICE,
    () =>
      new PaymentGatewayService(
        container.resolve(SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_REPOSITORY)
      ),
    { singleton: true }
  );

  container.register(
    SERVICE_IDENTIFIERS.PAYMENT_TRANSACTION_SERVICE,
    () =>
      new PaymentTransactionService(
        container.resolve(SERVICE_IDENTIFIERS.PAYMENT_TRANSACTION_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_SERVICE)
      ),
    { singleton: true }
  );

  // Email Marketing Services
  container.register(
    SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_SERVICE,
    () =>
      new EmailTemplateService(
        container.resolve(SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.EMAIL_LOG_REPOSITORY)
      ),
    { singleton: true }
  );

  container.register(
    SERVICE_IDENTIFIERS.EMAIL_LOG_SERVICE,
    () =>
      new EmailLogService(
        container.resolve(SERVICE_IDENTIFIERS.EMAIL_LOG_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.EMAIL_TEMPLATE_REPOSITORY)
      ),
    { singleton: true }
  );

  container.register(
    SERVICE_IDENTIFIERS.SMTP_CONFIG_SERVICE,
    () =>
      new SMTPConfigService(
        container.resolve(SERVICE_IDENTIFIERS.SMTP_CONFIG_REPOSITORY)
      ),
    { singleton: true }
  );

  console.log("All services registered successfully");
}

/**
 * Khởi tạo toàn bộ DI container
 */
export function initializeDI(): void {
  console.log("Initializing Dependency Injection Container...");

  // Setup repositories
  setupRepositories();

  // Setup services
  setupServices();

  console.log("DI Container initialized successfully");
  console.log("Registered services:", container.getRegisteredServices().length);
}

/**
 * Helper functions để lấy repositories từ container
 */
export const getRepositories = () => ({
  userRepository: container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY),
  adminUserRepository: container.resolve(
    SERVICE_IDENTIFIERS.ADMIN_USER_REPOSITORY
  ),
  productRepository: container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
  categoryRepository: container.resolve(
    SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY
  ),
  brandRepository: container.resolve(SERVICE_IDENTIFIERS.BRAND_REPOSITORY),
  orderRepository: container.resolve(SERVICE_IDENTIFIERS.ORDER_REPOSITORY),
  cartRepository: container.resolve(SERVICE_IDENTIFIERS.CART_REPOSITORY),
  addressRepository: container.resolve(SERVICE_IDENTIFIERS.ADDRESS_REPOSITORY),
  reviewRepository: container.resolve(SERVICE_IDENTIFIERS.REVIEW_REPOSITORY),
  wishlistRepository: container.resolve(
    SERVICE_IDENTIFIERS.WISHLIST_REPOSITORY
  ),
  postRepository: container.resolve(SERVICE_IDENTIFIERS.POST_REPOSITORY),
  pageRepository: container.resolve(SERVICE_IDENTIFIERS.PAGE_REPOSITORY),
  mediaRepository: container.resolve(SERVICE_IDENTIFIERS.MEDIA_REPOSITORY),
  menuRepository: container.resolve(SERVICE_IDENTIFIERS.MENU_REPOSITORY),
  settingRepository: container.resolve(SERVICE_IDENTIFIERS.SETTING_REPOSITORY),
  notificationRepository: container.resolve(
    SERVICE_IDENTIFIERS.NOTIFICATION_REPOSITORY
  ),
  auditLogRepository: container.resolve(
    SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY
  ),
  contactRepository: container.resolve(SERVICE_IDENTIFIERS.CONTACT_REPOSITORY),
  attributeRepository: container.resolve(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
  ),
  inventoryRepository: container.resolve(
    SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY
  ),
  promotionRepository: container.resolve(
    SERVICE_IDENTIFIERS.PROMOTION_REPOSITORY
  ),
  paymentGatewayRepository: container.resolve(
    SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_REPOSITORY
  ),
  paymentTransactionRepository: container.resolve(
    SERVICE_IDENTIFIERS.PAYMENT_TRANSACTION_REPOSITORY
  ),
});

/**
 * Helper functions để lấy services từ container
 */
export const getServices = () => ({
  userService: container.resolve<UserService>(SERVICE_IDENTIFIERS.USER_SERVICE),
  productService: container.resolve<ProductService>(
    SERVICE_IDENTIFIERS.PRODUCT_SERVICE
  ),
  categoryService: container.resolve<CategoryService>(
    SERVICE_IDENTIFIERS.CATEGORY_SERVICE
  ),
  orderService: container.resolve<OrderService>(
    SERVICE_IDENTIFIERS.ORDER_SERVICE
  ),
  cartService: container.resolve<CartService>(SERVICE_IDENTIFIERS.CART_SERVICE),
  brandService: container.resolve<BrandService>(
    SERVICE_IDENTIFIERS.BRAND_SERVICE
  ),
  addressService: container.resolve<AddressService>(
    SERVICE_IDENTIFIERS.ADDRESS_SERVICE
  ),
  reviewService: container.resolve<ReviewService>(
    SERVICE_IDENTIFIERS.REVIEW_SERVICE
  ),
  wishlistService: container.resolve<WishlistService>(
    SERVICE_IDENTIFIERS.WISHLIST_SERVICE
  ),
  mediaService: container.resolve<MediaService>(
    SERVICE_IDENTIFIERS.MEDIA_SERVICE
  ),
  menuService: container.resolve<MenuService>(SERVICE_IDENTIFIERS.MENU_SERVICE),
  seoService: container.resolve<SEOService>(SERVICE_IDENTIFIERS.SEO_SERVICE),
  adminUserService: container.resolve<AdminUserService>(
    SERVICE_IDENTIFIERS.ADMIN_USER_SERVICE
  ),
  settingService: container.resolve<SettingService>(
    SERVICE_IDENTIFIERS.SETTING_SERVICE
  ),
  notificationService: container.resolve<NotificationService>(
    SERVICE_IDENTIFIERS.NOTIFICATION_SERVICE
  ),
  auditLogService: container.resolve<AuditLogService>(
    SERVICE_IDENTIFIERS.AUDIT_LOG_SERVICE
  ),
  postService: container.resolve<PostService>(SERVICE_IDENTIFIERS.POST_SERVICE),
  pageService: container.resolve<PageService>(SERVICE_IDENTIFIERS.PAGE_SERVICE),
  contactService: container.resolve<ContactService>(
    SERVICE_IDENTIFIERS.CONTACT_SERVICE
  ),
  attributeService: container.resolve<AttributeService>(
    SERVICE_IDENTIFIERS.ATTRIBUTE_SERVICE
  ),
  inventoryService: container.resolve<InventoryService>(
    SERVICE_IDENTIFIERS.INVENTORY_SERVICE
  ),
  promotionService: container.resolve<PromotionService>(
    SERVICE_IDENTIFIERS.PROMOTION_SERVICE
  ),
  eventService: container.resolve<EventService>(
    SERVICE_IDENTIFIERS.EVENT_SERVICE
  ),
  notificationHandlersService: container.resolve<NotificationHandlersService>(
    SERVICE_IDENTIFIERS.NOTIFICATION_HANDLERS_SERVICE
  ),
  webSocketService: container.resolve<WebSocketService>(
    SERVICE_IDENTIFIERS.WEBSOCKET_SERVICE
  ),
  emailService: container.resolve<EmailService>(
    SERVICE_IDENTIFIERS.EMAIL_SERVICE
  ),
  metricsService: container.resolve<MetricsService>(
    SERVICE_IDENTIFIERS.METRICS_SERVICE
  ),
  rateLimitService: container.resolve<RateLimitService>(
    SERVICE_IDENTIFIERS.RATE_LIMIT_SERVICE
  ),
  paymentGatewayService: container.resolve<PaymentGatewayService>(
    SERVICE_IDENTIFIERS.PAYMENT_GATEWAY_SERVICE
  ),
  paymentTransactionService: container.resolve<PaymentTransactionService>(
    SERVICE_IDENTIFIERS.PAYMENT_TRANSACTION_SERVICE
  ),
});

// Note: DI container initialization is now handled manually in initialize.ts
// to prevent multiple container instances during Next.js compilation
