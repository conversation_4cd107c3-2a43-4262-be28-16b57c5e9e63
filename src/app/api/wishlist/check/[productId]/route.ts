import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import { WishlistService } from "../../../services/wishlist.service";
import { initializeSystem } from "../../../initialize";
import { UserRole } from "@/app/models/common.model";

// GET /api/wishlist/check/[productId] - <PERSON><PERSON><PERSON> tra sản phẩm có trong wishlist không
export async function GET(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const { productId } = params;

    if (!productId) {
      return NextResponse.json(
        { error: "Product ID là bắt buộc" },
        { status: 400 }
      );
    }

    // Initialize system and check wishlist using service
    initializeSystem();
    const wishlistService = container.resolve<WishlistService>(
      SERVICE_IDENTIFIERS.WISHLIST_SERVICE
    );

    // Create user entity for the service
    const user = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "USER") as UserRole,
    };

    const inWishlist = await wishlistService.isInWishlist(
      session.user.id,
      productId,
      user
    );

    return NextResponse.json({
      inWishlist,
    });
  } catch (error) {
    console.error("Check wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi kiểm tra danh sách yêu thích" },
      { status: 500 }
    );
  }
}
