import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../di-container";
import { WishlistService } from "../../services/wishlist.service";
import { initializeSystem } from "../../initialize";
import { UserRole } from "@/app/models/common.model";

const toggleWishlistSchema = z.object({
  productId: z.string().min(1, "Product ID là bắt buộc"),
});

// POST /api/wishlist/toggle - Toggle sản phẩm trong danh sách yêu thích
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { productId } = toggleWishlistSchema.parse(body);

    // Initialize system and toggle wishlist using service
    initializeSystem();
    const wishlistService = container.resolve<WishlistService>(
      SERVICE_IDENTIFIERS.WISHLIST_SERVICE
    );

    // Create user entity for the service
    const user = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "USER") as UserRole,
    };

    // Check if product is in wishlist
    const isInWishlist = await wishlistService.isInWishlist(
      session.user.id,
      productId,
      user
    );

    if (isInWishlist) {
      // Remove from wishlist
      await wishlistService.removeFromWishlist(
        session.user.id,
        productId,
        user
      );
      return NextResponse.json({
        action: "removed",
        message: "Đã xóa khỏi danh sách yêu thích",
      });
    } else {
      // Add to wishlist
      const wishlistItem = await wishlistService.addToWishlist(
        {
          userId: session.user.id,
          productId: productId,
        },
        user
      );
      return NextResponse.json({
        action: "added",
        message: "Đã thêm vào danh sách yêu thích",
        item: wishlistItem,
      });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Toggle wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thao tác với danh sách yêu thích" },
      { status: 500 }
    );
  }
}
