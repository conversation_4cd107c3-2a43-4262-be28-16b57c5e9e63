import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../../auth/[...nextauth]/route";
import { container, SERVICE_IDENTIFIERS } from "../../di-container";
import { initializeSystem } from "../../initialize";
import { WishlistService } from "../../services/wishlist.service";
import { UserRole } from "@/app/models/common.model";

// DELETE /api/wishlist/[productId] - <PERSON><PERSON><PERSON> sản phẩm khỏi danh sách yêu thích
export async function DELETE(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Initialize system and remove from wishlist using service
    initializeSystem();
    const wishlistService = container.resolve<WishlistService>(
      SERVICE_IDENTIFIERS.WISHLIST_SERVICE
    );

    // Create user entity for the service
    const user = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "USER") as UserRole,
    };

    // Since the service doesn't have a deleteByItemId method, we'll need to
    // access the repository directly to get the item first
    // This is not ideal but necessary for this specific route structure

    // The itemId parameter is actually productId for this endpoint
    const productId = params.itemId;

    await wishlistService.removeFromWishlist(session.user.id, productId, user);

    return NextResponse.json({
      message: "Đã xóa khỏi danh sách yêu thích",
    });
  } catch (error) {
    console.error("Remove from wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa khỏi danh sách yêu thích" },
      { status: 500 }
    );
  }
}
