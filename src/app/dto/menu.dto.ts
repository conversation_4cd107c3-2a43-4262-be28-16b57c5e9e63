/**
 * Menu DTO
 * Data Transfer Objects cho Menu module
 */

import { z } from "zod";

/**
 * Menu Item Type Enum (từ Prisma schema)
 */
export enum MenuItemType {
  LINK = "LINK",
  PAGE = "PAGE", 
  CATEGORY = "CATEGORY",
  PRODUCT = "PRODUCT",
  CUSTOM = "CUSTOM",
  SEPARATOR = "SEPARATOR"
}

/**
 * Menu DTO
 */
export interface MenuDTO {
  id: string;
  name: string;
  location: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Menu Item DTO
 */
export interface MenuItemDTO {
  id: string;
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Menu with Items DTO
 */
export interface MenuWithItemsDTO extends MenuDTO {
  items: MenuItemWithChildrenDTO[];
}

/**
 * Menu Item with Children DTO
 */
export interface MenuItemWithChildrenDTO extends MenuItemDTO {
  children?: MenuItemWithChildrenDTO[];
}

/**
 * Create Menu DTO
 */
export interface CreateMenuDTO {
  name: string;
  location: string;
  description?: string;
  isActive?: boolean;
}

/**
 * Update Menu DTO
 */
export interface UpdateMenuDTO {
  name?: string;
  location?: string;
  description?: string;
  isActive?: boolean;
}

/**
 * Create Menu Item DTO
 */
export interface CreateMenuItemDTO {
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type?: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Update Menu Item DTO
 */
export interface UpdateMenuItemDTO {
  parentId?: string;
  title?: string;
  url?: string;
  type?: MenuItemType;
  target?: string;
  icon?: string;
  cssClass?: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Menu Search Filters DTO
 */
export interface MenuSearchFiltersDTO {
  search?: string;
  location?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * Menu Stats DTO
 */
export interface MenuStatsDTO {
  total: number;
  active: number;
  inactive: number;
  totalItems: number;
}

/**
 * Menu Item Stats DTO
 */
export interface MenuItemStatsDTO {
  totalItems: number;
  activeItems: number;
  maxDepth: number;
}

/**
 * Zod Validation Schemas
 */

export const CreateMenuSchema = z.object({
  name: z.string().min(1, "Tên menu là bắt buộc").max(100, "Tên menu không được quá 100 ký tự"),
  location: z.string().min(1, "Vị trí menu là bắt buộc").max(50, "Vị trí menu không được quá 50 ký tự"),
  description: z.string().max(500, "Mô tả không được quá 500 ký tự").optional(),
  isActive: z.boolean().optional().default(true),
});

export const UpdateMenuSchema = z.object({
  name: z.string().min(1, "Tên menu là bắt buộc").max(100, "Tên menu không được quá 100 ký tự").optional(),
  location: z.string().min(1, "Vị trí menu là bắt buộc").max(50, "Vị trí menu không được quá 50 ký tự").optional(),
  description: z.string().max(500, "Mô tả không được quá 500 ký tự").optional(),
  isActive: z.boolean().optional(),
});

export const CreateMenuItemSchema = z.object({
  menuId: z.string().min(1, "Menu ID là bắt buộc"),
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc").max(200, "Tiêu đề không được quá 200 ký tự"),
  url: z.string().max(500, "URL không được quá 500 ký tự").optional(),
  type: z.nativeEnum(MenuItemType).optional().default(MenuItemType.LINK),
  target: z.string().max(20, "Target không được quá 20 ký tự").optional(),
  icon: z.string().max(100, "Icon không được quá 100 ký tự").optional(),
  cssClass: z.string().max(200, "CSS class không được quá 200 ký tự").optional(),
  order: z.number().int().min(0, "Thứ tự phải là số nguyên không âm").optional().default(0),
  isActive: z.boolean().optional().default(true),
});

export const UpdateMenuItemSchema = z.object({
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc").max(200, "Tiêu đề không được quá 200 ký tự").optional(),
  url: z.string().max(500, "URL không được quá 500 ký tự").optional(),
  type: z.nativeEnum(MenuItemType).optional(),
  target: z.string().max(20, "Target không được quá 20 ký tự").optional(),
  icon: z.string().max(100, "Icon không được quá 100 ký tự").optional(),
  cssClass: z.string().max(200, "CSS class không được quá 200 ký tự").optional(),
  order: z.number().int().min(0, "Thứ tự phải là số nguyên không âm").optional(),
  isActive: z.boolean().optional(),
});

export const MenuSearchFiltersSchema = z.object({
  search: z.string().optional(),
  location: z.string().optional(),
  isActive: z.boolean().optional(),
  page: z.number().int().min(1, "Trang phải lớn hơn 0").optional().default(1),
  limit: z.number().int().min(1, "Limit phải lớn hơn 0").max(100, "Limit không được quá 100").optional().default(20),
  sortBy: z.string().optional().default("name"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("asc"),
});

/**
 * Type exports for validation
 */
export type CreateMenuInput = z.infer<typeof CreateMenuSchema>;
export type UpdateMenuInput = z.infer<typeof UpdateMenuSchema>;
export type CreateMenuItemInput = z.infer<typeof CreateMenuItemSchema>;
export type UpdateMenuItemInput = z.infer<typeof UpdateMenuItemSchema>;
export type MenuSearchFiltersInput = z.infer<typeof MenuSearchFiltersSchema>;