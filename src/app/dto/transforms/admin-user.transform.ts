/**
 * Admin User Transform Functions
 * Transform functions giữa AdminUser entities và DTOs
 */

import { AdminUser, Prisma } from "@prisma/client";
import {
  AdminUserResponseDto,
  CreateAdminUserRequestDto,
  UpdateAdminUserRequestDto,
} from "../admin-user.dto";
import { AdminRoleDto } from "../common.dto";
import { AdminUserEntity, AdminRole } from "../../models/admin-user.model";

/**
 * Transform AdminUser entity to AdminUserResponseDto
 */
export function toAdminUserResponseDto(
  entity: AdminUserEntity | AdminUser
): AdminUserResponseDto {
  return {
    id: entity.id,
    email: entity.email,
    name: entity.name,
    avatar: entity.avatar || undefined,
    role: entity.role as AdminRoleDto,
    isActive: entity.isActive,
    permissions: Array.isArray(entity.permissions) 
      ? entity.permissions 
      : entity.permissions 
        ? Object.keys(entity.permissions as Record<string, any>).filter(
            key => (entity.permissions as Record<string, any>)[key]
          )
        : [],
    lastLoginAt: entity.lastLoginAt?.toISOString(),
    createdAt: entity.createdAt.toISOString(),
    updatedAt: entity.updatedAt.toISOString(),
    metadata: entity.metadata as Record<string, any> | undefined,
  };
}

/**
 * Transform CreateAdminUserRequestDto to AdminUserEntity data
 */
export function fromCreateAdminUserRequestDto(
  dto: CreateAdminUserRequestDto
): Partial<AdminUserEntity> {
  return {
    email: dto.email,
    name: dto.name,
    password: dto.password,
    role: dto.role as AdminRole,
    permissions: dto.permissions || [],
    avatar: dto.avatar,
    isActive: true,
  };
}

/**
 * Transform UpdateAdminUserRequestDto to AdminUserEntity data
 */
export function fromUpdateAdminUserRequestDto(
  dto: UpdateAdminUserRequestDto
): Partial<AdminUserEntity> {
  const result: Partial<AdminUserEntity> = {};

  if (dto.name !== undefined) result.name = dto.name;
  if (dto.avatar !== undefined) result.avatar = dto.avatar;
  if (dto.role !== undefined) result.role = dto.role as AdminRole;
  if (dto.permissions !== undefined) result.permissions = dto.permissions;
  if (dto.isActive !== undefined) result.isActive = dto.isActive;

  return result;
}

/**
 * Transform AdminUser with relations to AdminUserResponseDto
 */
export function toAdminUserResponseDtoWithRelations(
  entity: AdminUser & {
    avatar?: { url: string } | null;
    createdByAdmin?: { id: string; name: string; email: string } | null;
    _count?: {
      posts: number;
      pages: number;
      createdAdmins: number;
      auditLogs: number;
    };
  }
): AdminUserResponseDto & {
  avatarUrl?: string;
  createdBy?: { id: string; name: string; email: string };
  stats?: {
    posts: number;
    pages: number;
    createdAdmins: number;
    auditLogs: number;
  };
} {
  const baseDto = toAdminUserResponseDto(entity);
  
  return {
    ...baseDto,
    avatarUrl: entity.avatar?.url,
    createdBy: entity.createdByAdmin || undefined,
    stats: entity._count ? {
      posts: entity._count.posts,
      pages: entity._count.pages,
      createdAdmins: entity._count.createdAdmins,
      auditLogs: entity._count.auditLogs,
    } : undefined,
  };
}

/**
 * Transform array of AdminUser entities to AdminUserResponseDto array
 */
export function toAdminUserResponseDtoArray(
  entities: (AdminUserEntity | AdminUser)[]
): AdminUserResponseDto[] {
  return entities.map(toAdminUserResponseDto);
}

/**
 * Transform AdminRole enum to AdminRoleDto
 */
export function toAdminRoleDto(role: AdminRole): AdminRoleDto {
  switch (role) {
    case AdminRole.SUPER_ADMIN:
      return AdminRoleDto.SUPER_ADMIN;
    case AdminRole.ADMIN:
      return AdminRoleDto.ADMIN;
    case AdminRole.MODERATOR:
      return AdminRoleDto.MODERATOR;
    default:
      return AdminRoleDto.MODERATOR;
  }
}

/**
 * Transform AdminRoleDto to AdminRole enum
 */
export function fromAdminRoleDto(roleDto: AdminRoleDto): AdminRole {
  switch (roleDto) {
    case AdminRoleDto.SUPER_ADMIN:
      return AdminRole.SUPER_ADMIN;
    case AdminRoleDto.ADMIN:
      return AdminRole.ADMIN;
    case AdminRoleDto.MODERATOR:
      return AdminRole.MODERATOR;
    default:
      return AdminRole.MODERATOR;
  }
}

/**
 * Transform permissions array to permissions object
 */
export function toPermissionsObject(permissions: string[]): Record<string, boolean> {
  const result: Record<string, boolean> = {};
  permissions.forEach(permission => {
    result[permission] = true;
  });
  return result;
}

/**
 * Transform permissions object to permissions array
 */
export function fromPermissionsObject(permissions: Record<string, boolean>): string[] {
  return Object.keys(permissions).filter(key => permissions[key]);
}

/**
 * Validate and transform admin user search filters
 */
export function transformAdminUserSearchFilters(filters: any): {
  search?: string;
  role?: AdminRole;
  isActive?: boolean;
  department?: string;
  dateFrom?: Date;
  dateTo?: Date;
} {
  const result: any = {};

  if (filters.search && typeof filters.search === 'string') {
    result.search = filters.search.trim();
  }

  if (filters.role && Object.values(AdminRole).includes(filters.role)) {
    result.role = filters.role;
  }

  if (filters.isActive !== undefined) {
    result.isActive = Boolean(filters.isActive);
  }

  if (filters.department && typeof filters.department === 'string') {
    result.department = filters.department.trim();
  }

  if (filters.dateFrom) {
    const date = new Date(filters.dateFrom);
    if (!isNaN(date.getTime())) {
      result.dateFrom = date;
    }
  }

  if (filters.dateTo) {
    const date = new Date(filters.dateTo);
    if (!isNaN(date.getTime())) {
      result.dateTo = date;
    }
  }

  return result;
}

/**
 * Transform Prisma AdminUser to AdminUserEntity
 */
export function toAdminUserEntity(prismaAdminUser: AdminUser): AdminUserEntity {
  return {
    id: prismaAdminUser.id,
    email: prismaAdminUser.email,
    name: prismaAdminUser.name,
    password: prismaAdminUser.password,
    avatar: prismaAdminUser.avatarId || undefined,
    role: prismaAdminUser.role as AdminRole,
    isActive: prismaAdminUser.isActive,
    permissions: Array.isArray(prismaAdminUser.permissions)
      ? prismaAdminUser.permissions as string[]
      : prismaAdminUser.permissions
        ? Object.keys(prismaAdminUser.permissions as Record<string, any>).filter(
            key => (prismaAdminUser.permissions as Record<string, any>)[key]
          )
        : [],
    lastLoginAt: prismaAdminUser.lastLoginAt || undefined,
    metadata: prismaAdminUser.permissions as Record<string, any> | undefined,
    createdAt: prismaAdminUser.createdAt,
    updatedAt: prismaAdminUser.updatedAt,
  };
}

/**
 * Transform AdminUserEntity to Prisma AdminUser create input
 */
export function toPrismaAdminUserCreateInput(
  entity: Partial<AdminUserEntity>
): Prisma.AdminUserCreateInput {
  return {
    email: entity.email!,
    name: entity.name!,
    password: entity.password!,
    role: entity.role!,
    avatarId: entity.avatar || undefined,
    isActive: entity.isActive ?? true,
    permissions: entity.permissions 
      ? toPermissionsObject(entity.permissions) as Prisma.InputJsonValue
      : undefined,
    lastLoginAt: entity.lastLoginAt || undefined,
  };
}

/**
 * Transform AdminUserEntity to Prisma AdminUser update input
 */
export function toPrismaAdminUserUpdateInput(
  entity: Partial<AdminUserEntity>
): Prisma.AdminUserUpdateInput {
  const result: Prisma.AdminUserUpdateInput = {};

  if (entity.name !== undefined) result.name = entity.name;
  if (entity.password !== undefined) result.password = entity.password;
  if (entity.role !== undefined) result.role = entity.role;
  if (entity.avatar !== undefined) result.avatarId = entity.avatar;
  if (entity.isActive !== undefined) result.isActive = entity.isActive;
  if (entity.permissions !== undefined) {
    result.permissions = toPermissionsObject(entity.permissions) as Prisma.InputJsonValue;
  }
  if (entity.lastLoginAt !== undefined) result.lastLoginAt = entity.lastLoginAt;

  return result;
}
