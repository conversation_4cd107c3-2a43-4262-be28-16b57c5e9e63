/**
 * SEO Transform Functions
 * Chuyển đổi giữa Prisma models và DTOs
 */

import { SEOSettings, PageSEO, Prisma } from "@prisma/client";
import {
  SEOSettingsDTO,
  PageSEODTO,
  CreateSEOSettingsDTO,
  UpdateSEOSettingsDTO,
  CreatePageSEODTO,
  UpdatePageSEODTO,
} from "../seo.dto";

/**
 * Transform SEOSettings từ Prisma model sang DTO
 */
export function transformSEOSettingsToDTO(seoSettings: SEOSettings): SEOSettingsDTO {
  return {
    id: seoSettings.id,
    siteName: seoSettings.siteName || undefined,
    siteDescription: seoSettings.siteDescription || undefined,
    siteKeywords: seoSettings.siteKeywords,
    defaultTitle: seoSettings.defaultTitle || undefined,
    titleTemplate: seoSettings.titleTemplate || undefined,
    defaultDescription: seoSettings.defaultDescription || undefined,
    defaultImage: seoSettings.defaultImage || undefined,
    ogSiteName: seoSettings.ogSiteName || undefined,
    ogType: seoSettings.ogType || undefined,
    twitterSite: seoSettings.twitterSite || undefined,
    twitterCreator: seoSettings.twitterCreator || undefined,
    robotsTxt: seoSettings.robotsTxt || undefined,
    sitemapEnabled: seoSettings.sitemapEnabled,
    sitemapFrequency: seoSettings.sitemapFrequency || undefined,
    googleAnalyticsId: seoSettings.googleAnalyticsId || undefined,
    googleTagManagerId: seoSettings.googleTagManagerId || undefined,
    facebookPixelId: seoSettings.facebookPixelId || undefined,
    organizationName: seoSettings.organizationName || undefined,
    organizationLogo: seoSettings.organizationLogo || undefined,
    organizationType: seoSettings.organizationType || undefined,
    contactEmail: seoSettings.contactEmail || undefined,
    contactPhone: seoSettings.contactPhone || undefined,
    address: seoSettings.address as Record<string, any> | undefined,
    googleSiteVerification: seoSettings.googleSiteVerification || undefined,
    bingSiteVerification: seoSettings.bingSiteVerification || undefined,
    createdAt: seoSettings.createdAt.toISOString(),
    updatedAt: seoSettings.updatedAt.toISOString(),
  };
}

/**
 * Transform PageSEO từ Prisma model sang DTO
 */
export function transformPageSEOToDTO(pageSEO: PageSEO): PageSEODTO {
  return {
    id: pageSEO.id,
    path: pageSEO.path,
    title: pageSEO.title || undefined,
    description: pageSEO.description || undefined,
    keywords: pageSEO.keywords,
    ogTitle: pageSEO.ogTitle || undefined,
    ogDescription: pageSEO.ogDescription || undefined,
    ogImage: pageSEO.ogImage || undefined,
    ogType: pageSEO.ogType || undefined,
    twitterTitle: pageSEO.twitterTitle || undefined,
    twitterDescription: pageSEO.twitterDescription || undefined,
    twitterImage: pageSEO.twitterImage || undefined,
    twitterCard: pageSEO.twitterCard || undefined,
    canonical: pageSEO.canonical || undefined,
    noindex: pageSEO.noindex,
    nofollow: pageSEO.nofollow,
    priority: pageSEO.priority || undefined,
    changefreq: pageSEO.changefreq || undefined,
    isActive: pageSEO.isActive,
    createdAt: pageSEO.createdAt.toISOString(),
    updatedAt: pageSEO.updatedAt.toISOString(),
  };
}

/**
 * Transform CreateSEOSettingsDTO sang Prisma input
 */
export function transformCreateSEOSettingsDTOToPrisma(
  dto: CreateSEOSettingsDTO
): Prisma.SEOSettingsCreateInput {
  return {
    siteName: dto.siteName,
    siteDescription: dto.siteDescription,
    siteKeywords: dto.siteKeywords || [],
    defaultTitle: dto.defaultTitle,
    titleTemplate: dto.titleTemplate,
    defaultDescription: dto.defaultDescription,
    defaultImage: dto.defaultImage,
    ogSiteName: dto.ogSiteName,
    ogType: dto.ogType,
    twitterSite: dto.twitterSite,
    twitterCreator: dto.twitterCreator,
    robotsTxt: dto.robotsTxt,
    sitemapEnabled: dto.sitemapEnabled ?? true,
    sitemapFrequency: dto.sitemapFrequency,
    googleAnalyticsId: dto.googleAnalyticsId,
    googleTagManagerId: dto.googleTagManagerId,
    facebookPixelId: dto.facebookPixelId,
    organizationName: dto.organizationName,
    organizationLogo: dto.organizationLogo,
    organizationType: dto.organizationType,
    contactEmail: dto.contactEmail,
    contactPhone: dto.contactPhone,
    address: dto.address,
    googleSiteVerification: dto.googleSiteVerification,
    bingSiteVerification: dto.bingSiteVerification,
  };
}

/**
 * Transform UpdateSEOSettingsDTO sang Prisma input
 */
export function transformUpdateSEOSettingsDTOToPrisma(
  dto: UpdateSEOSettingsDTO
): Prisma.SEOSettingsUpdateInput {
  const updateData: Prisma.SEOSettingsUpdateInput = {};

  if (dto.siteName !== undefined) updateData.siteName = dto.siteName;
  if (dto.siteDescription !== undefined) updateData.siteDescription = dto.siteDescription;
  if (dto.siteKeywords !== undefined) updateData.siteKeywords = dto.siteKeywords;
  if (dto.defaultTitle !== undefined) updateData.defaultTitle = dto.defaultTitle;
  if (dto.titleTemplate !== undefined) updateData.titleTemplate = dto.titleTemplate;
  if (dto.defaultDescription !== undefined) updateData.defaultDescription = dto.defaultDescription;
  if (dto.defaultImage !== undefined) updateData.defaultImage = dto.defaultImage;
  if (dto.ogSiteName !== undefined) updateData.ogSiteName = dto.ogSiteName;
  if (dto.ogType !== undefined) updateData.ogType = dto.ogType;
  if (dto.twitterSite !== undefined) updateData.twitterSite = dto.twitterSite;
  if (dto.twitterCreator !== undefined) updateData.twitterCreator = dto.twitterCreator;
  if (dto.robotsTxt !== undefined) updateData.robotsTxt = dto.robotsTxt;
  if (dto.sitemapEnabled !== undefined) updateData.sitemapEnabled = dto.sitemapEnabled;
  if (dto.sitemapFrequency !== undefined) updateData.sitemapFrequency = dto.sitemapFrequency;
  if (dto.googleAnalyticsId !== undefined) updateData.googleAnalyticsId = dto.googleAnalyticsId;
  if (dto.googleTagManagerId !== undefined) updateData.googleTagManagerId = dto.googleTagManagerId;
  if (dto.facebookPixelId !== undefined) updateData.facebookPixelId = dto.facebookPixelId;
  if (dto.organizationName !== undefined) updateData.organizationName = dto.organizationName;
  if (dto.organizationLogo !== undefined) updateData.organizationLogo = dto.organizationLogo;
  if (dto.organizationType !== undefined) updateData.organizationType = dto.organizationType;
  if (dto.contactEmail !== undefined) updateData.contactEmail = dto.contactEmail;
  if (dto.contactPhone !== undefined) updateData.contactPhone = dto.contactPhone;
  if (dto.address !== undefined) updateData.address = dto.address;
  if (dto.googleSiteVerification !== undefined) updateData.googleSiteVerification = dto.googleSiteVerification;
  if (dto.bingSiteVerification !== undefined) updateData.bingSiteVerification = dto.bingSiteVerification;

  return updateData;
}

/**
 * Transform CreatePageSEODTO sang Prisma input
 */
export function transformCreatePageSEODTOToPrisma(
  dto: CreatePageSEODTO
): Prisma.PageSEOCreateInput {
  return {
    path: dto.path,
    title: dto.title,
    description: dto.description,
    keywords: dto.keywords || [],
    ogTitle: dto.ogTitle,
    ogDescription: dto.ogDescription,
    ogImage: dto.ogImage,
    ogType: dto.ogType,
    twitterTitle: dto.twitterTitle,
    twitterDescription: dto.twitterDescription,
    twitterImage: dto.twitterImage,
    twitterCard: dto.twitterCard,
    canonical: dto.canonical,
    noindex: dto.noindex ?? false,
    nofollow: dto.nofollow ?? false,
    priority: dto.priority,
    changefreq: dto.changefreq,
    isActive: dto.isActive ?? true,
  };
}

/**
 * Transform UpdatePageSEODTO sang Prisma input
 */
export function transformUpdatePageSEODTOToPrisma(
  dto: UpdatePageSEODTO
): Prisma.PageSEOUpdateInput {
  const updateData: Prisma.PageSEOUpdateInput = {};

  if (dto.title !== undefined) updateData.title = dto.title;
  if (dto.description !== undefined) updateData.description = dto.description;
  if (dto.keywords !== undefined) updateData.keywords = dto.keywords;
  if (dto.ogTitle !== undefined) updateData.ogTitle = dto.ogTitle;
  if (dto.ogDescription !== undefined) updateData.ogDescription = dto.ogDescription;
  if (dto.ogImage !== undefined) updateData.ogImage = dto.ogImage;
  if (dto.ogType !== undefined) updateData.ogType = dto.ogType;
  if (dto.twitterTitle !== undefined) updateData.twitterTitle = dto.twitterTitle;
  if (dto.twitterDescription !== undefined) updateData.twitterDescription = dto.twitterDescription;
  if (dto.twitterImage !== undefined) updateData.twitterImage = dto.twitterImage;
  if (dto.twitterCard !== undefined) updateData.twitterCard = dto.twitterCard;
  if (dto.canonical !== undefined) updateData.canonical = dto.canonical;
  if (dto.noindex !== undefined) updateData.noindex = dto.noindex;
  if (dto.nofollow !== undefined) updateData.nofollow = dto.nofollow;
  if (dto.priority !== undefined) updateData.priority = dto.priority;
  if (dto.changefreq !== undefined) updateData.changefreq = dto.changefreq;
  if (dto.isActive !== undefined) updateData.isActive = dto.isActive;

  return updateData;
}

/**
 * Generate meta tags từ SEO data
 */
export function generateMetaTags(
  pageSEO?: PageSEODTO,
  globalSEO?: SEOSettingsDTO
): Record<string, string> {
  const metaTags: Record<string, string> = {};

  // Title
  const title = pageSEO?.title || globalSEO?.defaultTitle;
  if (title) {
    if (globalSEO?.titleTemplate && globalSEO.titleTemplate.includes("%s")) {
      metaTags.title = globalSEO.titleTemplate.replace("%s", title);
    } else if (globalSEO?.siteName) {
      metaTags.title = `${title} | ${globalSEO.siteName}`;
    } else {
      metaTags.title = title;
    }
  }

  // Description
  const description = pageSEO?.description || globalSEO?.defaultDescription;
  if (description) {
    metaTags.description = description;
  }

  // Keywords
  const keywords = [
    ...(pageSEO?.keywords || []),
    ...(globalSEO?.siteKeywords || [])
  ];
  if (keywords.length > 0) {
    metaTags.keywords = keywords.join(", ");
  }

  // Open Graph
  const ogTitle = pageSEO?.ogTitle || title;
  if (ogTitle) {
    metaTags["og:title"] = ogTitle;
  }

  const ogDescription = pageSEO?.ogDescription || description;
  if (ogDescription) {
    metaTags["og:description"] = ogDescription;
  }

  const ogImage = pageSEO?.ogImage || globalSEO?.defaultImage;
  if (ogImage) {
    metaTags["og:image"] = ogImage;
  }

  const ogType = pageSEO?.ogType || "website";
  metaTags["og:type"] = ogType;

  if (globalSEO?.ogSiteName) {
    metaTags["og:site_name"] = globalSEO.ogSiteName;
  }

  // Twitter
  const twitterTitle = pageSEO?.twitterTitle || ogTitle;
  if (twitterTitle) {
    metaTags["twitter:title"] = twitterTitle;
  }

  const twitterDescription = pageSEO?.twitterDescription || ogDescription;
  if (twitterDescription) {
    metaTags["twitter:description"] = twitterDescription;
  }

  const twitterImage = pageSEO?.twitterImage || ogImage;
  if (twitterImage) {
    metaTags["twitter:image"] = twitterImage;
  }

  const twitterCard = pageSEO?.twitterCard || "summary";
  metaTags["twitter:card"] = twitterCard;

  if (globalSEO?.twitterSite) {
    metaTags["twitter:site"] = globalSEO.twitterSite;
  }

  if (globalSEO?.twitterCreator) {
    metaTags["twitter:creator"] = globalSEO.twitterCreator;
  }

  // Canonical
  if (pageSEO?.canonical) {
    metaTags.canonical = pageSEO.canonical;
  }

  // Robots
  const robots: string[] = [];
  if (pageSEO?.noindex) robots.push("noindex");
  if (pageSEO?.nofollow) robots.push("nofollow");
  if (robots.length > 0) {
    metaTags.robots = robots.join(", ");
  }

  return metaTags;
}

/**
 * Generate structured data từ SEO settings
 */
export function generateStructuredData(
  globalSEO: SEOSettingsDTO,
  pageSEO?: PageSEODTO
): Record<string, any> {
  const structuredData: Record<string, any> = {
    "@context": "https://schema.org",
    "@type": globalSEO.organizationType || "Organization",
  };

  if (globalSEO.organizationName || globalSEO.siteName) {
    structuredData.name = globalSEO.organizationName || globalSEO.siteName;
  }

  if (globalSEO.organizationLogo) {
    structuredData.logo = globalSEO.organizationLogo;
  }

  if (globalSEO.contactEmail) {
    structuredData.email = globalSEO.contactEmail;
  }

  if (globalSEO.contactPhone) {
    structuredData.telephone = globalSEO.contactPhone;
  }

  if (globalSEO.address) {
    structuredData.address = globalSEO.address;
  }

  if (globalSEO.siteDescription) {
    structuredData.description = globalSEO.siteDescription;
  }

  return structuredData;
}

/**
 * Validate SEO data quality
 */
export function validateSEOQuality(pageSEO: PageSEODTO): {
  score: number;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];
  let score = 100;

  // Title validation
  if (!pageSEO.title) {
    issues.push("Missing page title");
    score -= 20;
  } else {
    if (pageSEO.title.length < 30) {
      suggestions.push("Title could be longer (30-60 characters recommended)");
      score -= 5;
    }
    if (pageSEO.title.length > 60) {
      issues.push("Title too long (over 60 characters)");
      score -= 10;
    }
  }

  // Description validation
  if (!pageSEO.description) {
    issues.push("Missing meta description");
    score -= 15;
  } else {
    if (pageSEO.description.length < 120) {
      suggestions.push("Description could be longer (120-160 characters recommended)");
      score -= 5;
    }
    if (pageSEO.description.length > 160) {
      issues.push("Description too long (over 160 characters)");
      score -= 10;
    }
  }

  // Keywords validation
  if (pageSEO.keywords.length === 0) {
    suggestions.push("Consider adding relevant keywords");
    score -= 5;
  } else if (pageSEO.keywords.length > 10) {
    issues.push("Too many keywords (max 10 recommended)");
    score -= 10;
  }

  // Open Graph validation
  if (!pageSEO.ogTitle && !pageSEO.title) {
    suggestions.push("Add Open Graph title for better social sharing");
    score -= 5;
  }

  if (!pageSEO.ogDescription && !pageSEO.description) {
    suggestions.push("Add Open Graph description for better social sharing");
    score -= 5;
  }

  if (!pageSEO.ogImage) {
    suggestions.push("Add Open Graph image for better social sharing");
    score -= 5;
  }

  // Canonical URL
  if (!pageSEO.canonical) {
    suggestions.push("Consider adding canonical URL to prevent duplicate content");
    score -= 3;
  }

  return {
    score: Math.max(0, score),
    issues,
    suggestions,
  };
}