/**
 * Notification Transform Functions
 * Chuyển đổi dữ liệu giữa các layers cho Admin Notifications
 */

import { Notification } from "@prisma/client";
import { 
  NotificationResponseDto, 
  AdminUserDto,
  NotificationTypeDto,
  NotificationPriorityDto,
  NotificationTargetDto 
} from "../notification.dto";
import { AdminNotificationEntity } from "../../models/admin-notification.model";

/**
 * Transform Prisma Notification to Response DTO
 */
export function transformNotificationToDto(
  notification: Notification & {
    creator?: { id: string; name: string; email: string } | null;
    target?: { id: string; name: string; email: string } | null;
  }
): NotificationResponseDto {
  return {
    id: notification.id,
    title: notification.title,
    message: notification.message,
    type: notification.type as NotificationTypeDto,
    priority: notification.priority as NotificationPriorityDto,
    targetType: notification.targetType as NotificationTargetDto,
    targetId: notification.targetId || undefined,
    isRead: notification.isRead,
    readAt: notification.readAt?.toISOString(),
    actionUrl: notification.actionUrl || undefined,
    metadata: notification.metadata as Record<string, any> || undefined,
    expiresAt: notification.expiresAt?.toISOString(),
    createdBy: notification.createdBy || undefined,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
    creator: notification.creator ? transformAdminUserToDto(notification.creator) : undefined,
    target: notification.target ? transformAdminUserToDto(notification.target) : undefined,
  };
}

/**
 * Transform Admin User to DTO
 */
export function transformAdminUserToDto(adminUser: {
  id: string;
  name: string;
  email: string;
}): AdminUserDto {
  return {
    id: adminUser.id,
    name: adminUser.name,
    email: adminUser.email,
  };
}

/**
 * Transform Prisma Notification to Entity
 */
export function transformNotificationToEntity(
  notification: Notification & {
    creator?: { id: string; name: string; email: string; role: string } | null;
    target?: { id: string; name: string; email: string; role: string } | null;
  }
): AdminNotificationEntity {
  return {
    id: notification.id,
    title: notification.title,
    message: notification.message,
    type: notification.type as any,
    priority: notification.priority as any,
    targetType: notification.targetType as any,
    targetId: notification.targetId || undefined,
    isRead: notification.isRead,
    readAt: notification.readAt || undefined,
    actionUrl: notification.actionUrl || undefined,
    metadata: notification.metadata as Record<string, any> || undefined,
    expiresAt: notification.expiresAt || undefined,
    createdBy: notification.createdBy || undefined,
    createdAt: notification.createdAt,
    updatedAt: notification.updatedAt,
    creator: notification.creator ? {
      id: notification.creator.id,
      name: notification.creator.name,
      email: notification.creator.email,
      role: notification.creator.role,
    } : undefined,
    target: notification.target ? {
      id: notification.target.id,
      name: notification.target.name,
      email: notification.target.email,
      role: notification.target.role,
    } : undefined,
  };
}

/**
 * Transform array of notifications to DTOs
 */
export function transformNotificationsToDto(
  notifications: (Notification & {
    creator?: { id: string; name: string; email: string } | null;
    target?: { id: string; name: string; email: string } | null;
  })[]
): NotificationResponseDto[] {
  return notifications.map(transformNotificationToDto);
}

/**
 * Transform array of notifications to entities
 */
export function transformNotificationsToEntity(
  notifications: (Notification & {
    creator?: { id: string; name: string; email: string; role: string } | null;
    target?: { id: string; name: string; email: string; role: string } | null;
  })[]
): AdminNotificationEntity[] {
  return notifications.map(transformNotificationToEntity);
}

/**
 * Transform DTO to Prisma create data
 */
export function transformCreateDtoToPrisma(dto: {
  title: string;
  message: string;
  type?: NotificationTypeDto;
  priority?: NotificationPriorityDto;
  targetType?: NotificationTargetDto;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
  createdBy?: string;
}) {
  return {
    title: dto.title,
    message: dto.message,
    type: dto.type || NotificationTypeDto.INFO,
    priority: dto.priority || NotificationPriorityDto.NORMAL,
    targetType: dto.targetType || NotificationTargetDto.ALL_ADMINS,
    targetId: dto.targetId || null,
    actionUrl: dto.actionUrl || null,
    metadata: dto.metadata || null,
    expiresAt: dto.expiresAt ? new Date(dto.expiresAt) : null,
    createdBy: dto.createdBy || null,
  };
}

/**
 * Transform DTO to Prisma update data
 */
export function transformUpdateDtoToPrisma(dto: {
  title?: string;
  message?: string;
  type?: NotificationTypeDto;
  priority?: NotificationPriorityDto;
  targetType?: NotificationTargetDto;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
  isRead?: boolean;
}) {
  const updateData: any = {};

  if (dto.title !== undefined) updateData.title = dto.title;
  if (dto.message !== undefined) updateData.message = dto.message;
  if (dto.type !== undefined) updateData.type = dto.type;
  if (dto.priority !== undefined) updateData.priority = dto.priority;
  if (dto.targetType !== undefined) updateData.targetType = dto.targetType;
  if (dto.targetId !== undefined) updateData.targetId = dto.targetId || null;
  if (dto.actionUrl !== undefined) updateData.actionUrl = dto.actionUrl || null;
  if (dto.metadata !== undefined) updateData.metadata = dto.metadata || null;
  if (dto.expiresAt !== undefined) updateData.expiresAt = dto.expiresAt ? new Date(dto.expiresAt) : null;
  if (dto.isRead !== undefined) {
    updateData.isRead = dto.isRead;
    updateData.readAt = dto.isRead ? new Date() : null;
  }

  return updateData;
}