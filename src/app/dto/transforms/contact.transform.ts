/**
 * Contact Transform Functions
 * <PERSON>yển đổi gi<PERSON>a ContactEntity và ContactDto
 */

import { ContactEntity } from "../../models/contact.model";
import { 
  ContactDto, 
  ContactResponseDto, 
  CreateContactDto, 
  UpdateContactDto,
  ContactNoteDto,
  CreateContactNoteDto
} from "../contact.dto";
import { ContactStatus, ContactPriority } from "@prisma/client";
import { ContactStatus as ModelContactStatus, ContactPriority as ModelContactPriority } from "../../models/contact.model";
import { removeUndefined } from "./common.transform";

/**
 * Transform ContactEntity to ContactDto
 */
export function contactToDto(contact: ContactEntity): ContactDto {
  return removeUndefined({
    id: contact.id,
    name: contact.name,
    email: contact.email,
    phone: contact.phone,
    company: undefined, // Not in model
    service: undefined, // Not in model
    subject: contact.subject,
    message: contact.message,
    status: contact.status,
    priority: contact.priority,
    source: "website", // Default value
    ipAddress: undefined, // Not in model
    userAgent: undefined, // Not in model
    assignedTo: contact.assignedTo,
    respondedAt: contact.respondedAt?.toISOString(),
    responseMessage: contact.response,
    tags: [], // Default empty array
    metadata: contact.metadata,
    createdAt: contact.createdAt.toISOString(),
    updatedAt: contact.updatedAt.toISOString(),
  }) as ContactDto;
}

/**
 * Transform ContactEntity to ContactResponseDto with relations
 */
export function contactToResponseDto(contact: ContactEntity & {
  assignedAdmin?: any;
  notes?: any[];
}): ContactResponseDto {
  const baseDto = contactToDto(contact);
  
  return {
    ...baseDto,
    assignedAdmin: contact.assignedAdmin ? {
      id: contact.assignedAdmin.id,
      name: contact.assignedAdmin.name,
      email: contact.assignedAdmin.email,
    } : undefined,
    notes: contact.notes ? contact.notes.map(note => ({
      id: note.id,
      note: note.note,
      isInternal: note.isInternal,
      createdAt: note.createdAt.toISOString(),
      updatedAt: note.updatedAt.toISOString(),
      admin: {
        id: note.admin.id,
        name: note.admin.name,
        email: note.admin.email,
      },
    })) : [],
    notesCount: contact.notes ? contact.notes.length : 0,
  };
}

/**
 * Transform CreateContactDto to ContactEntity data
 */
export function createContactDtoToEntity(
  dto: CreateContactDto,
  additionalData?: {
    ipAddress?: string;
    userAgent?: string;
  }
): Omit<ContactEntity, 'id' | 'createdAt' | 'updatedAt' | 'respondedAt'> {
  return removeUndefined({
    name: dto.name,
    email: dto.email,
    phone: dto.phone,
    subject: dto.subject || "",
    message: dto.message,
    status: dto.status as any, // Type conversion
    priority: dto.priority as any, // Type conversion
    assignedTo: dto.assignedTo,
    response: undefined,
    metadata: dto.metadata,
  }) as Omit<ContactEntity, 'id' | 'createdAt' | 'updatedAt' | 'respondedAt'>;
}

/**
 * Transform UpdateContactDto to partial ContactEntity data
 */
export function updateContactDtoToEntity(dto: UpdateContactDto): Partial<ContactEntity> {
  return removeUndefined({
    name: dto.name,
    email: dto.email,
    phone: dto.phone,
    subject: dto.subject,
    message: dto.message,
    status: dto.status as any, // Type conversion
    priority: dto.priority as any, // Type conversion
    assignedTo: dto.assignedTo,
    response: dto.responseMessage,
    metadata: dto.metadata,
    respondedAt: dto.responseMessage ? new Date() : undefined,
  });
}

/**
 * Transform ContactNote to ContactNoteDto
 */
export function contactNoteToDto(note: any): ContactNoteDto {
  return {
    id: note.id,
    contactId: note.contactId,
    adminId: note.adminId,
    note: note.note,
    isInternal: note.isInternal,
    createdAt: note.createdAt.toISOString(),
    updatedAt: note.updatedAt.toISOString(),
  };
}

/**
 * Transform CreateContactNoteDto to ContactNote data
 */
export function createContactNoteDtoToEntity(
  dto: CreateContactNoteDto,
  contactId: string,
  adminId: string
): any {
  return {
    contactId,
    adminId,
    note: dto.note,
    isInternal: dto.isInternal,
  };
}

/**
 * Transform array of ContactEntity to array of ContactResponseDto
 */
export function contactsToResponseDtos(contacts: (ContactEntity & {
  assignedAdmin?: any;
  notes?: any[];
})[]): ContactResponseDto[] {
  return contacts.map(contactToResponseDto);
}

/**
 * Transform array of ContactEntity to array of ContactDto
 */
export function contactsToDtos(contacts: ContactEntity[]): ContactDto[] {
  return contacts.map(contactToDto);
}

/**
 * Transform Prisma contact to ContactEntity
 */
export function prismaContactToEntity(prismaContact: any): ContactEntity {
  return {
    id: prismaContact.id,
    name: prismaContact.name,
    email: prismaContact.email,
    phone: prismaContact.phone,
    subject: prismaContact.subject,
    message: prismaContact.message,
    status: prismaContact.status,
    priority: prismaContact.priority,
    assignedTo: prismaContact.assignedTo,
    response: prismaContact.responseMessage,
    respondedAt: prismaContact.respondedAt,
    metadata: prismaContact.metadata,
    createdAt: prismaContact.createdAt,
    updatedAt: prismaContact.updatedAt,
  };
}

/**
 * Transform ContactEntity to Prisma data
 */
export function contactEntityToPrisma(contact: Partial<ContactEntity>): any {
  return removeUndefined({
    name: contact.name,
    email: contact.email,
    phone: contact.phone,
    subject: contact.subject,
    message: contact.message,
    status: contact.status,
    priority: contact.priority,
    assignedTo: contact.assignedTo,
    respondedAt: contact.respondedAt,
    responseMessage: contact.response,
    metadata: contact.metadata,
  });
}

/**
 * Transform Prisma contact note to ContactNote
 */
export function prismaContactNoteToEntity(prismaNote: any): any {
  return {
    id: prismaNote.id,
    contactId: prismaNote.contactId,
    adminId: prismaNote.adminId,
    note: prismaNote.note,
    isInternal: prismaNote.isInternal,
    createdAt: prismaNote.createdAt,
    updatedAt: prismaNote.updatedAt,
  };
}

/**
 * Helper function to get contact status display text
 */
export function getContactStatusDisplay(status: ContactStatus): string {
  const statusMap: Record<ContactStatus, string> = {
    [ContactStatus.NEW]: "Mới",
    [ContactStatus.OPEN]: "Đang xử lý",
    [ContactStatus.IN_PROGRESS]: "Đang thực hiện",
    [ContactStatus.WAITING_CUSTOMER]: "Chờ khách hàng",
    [ContactStatus.RESOLVED]: "Đã giải quyết",
    [ContactStatus.CLOSED]: "Đã đóng",
    [ContactStatus.SPAM]: "Spam",
  };
  return statusMap[status] || status;
}

/**
 * Helper function to get contact priority display text
 */
export function getContactPriorityDisplay(priority: ContactPriority): string {
  const priorityMap: Record<ContactPriority, string> = {
    [ContactPriority.LOW]: "Thấp",
    [ContactPriority.NORMAL]: "Bình thường",
    [ContactPriority.HIGH]: "Cao",
    [ContactPriority.URGENT]: "Khẩn cấp",
  };
  return priorityMap[priority] || priority;
}

/**
 * Helper function to get contact status color class
 */
export function getContactStatusColor(status: ContactStatus): string {
  const colorMap: Record<ContactStatus, string> = {
    [ContactStatus.NEW]: "bg-blue-100 text-blue-800",
    [ContactStatus.OPEN]: "bg-yellow-100 text-yellow-800",
    [ContactStatus.IN_PROGRESS]: "bg-orange-100 text-orange-800",
    [ContactStatus.WAITING_CUSTOMER]: "bg-purple-100 text-purple-800",
    [ContactStatus.RESOLVED]: "bg-green-100 text-green-800",
    [ContactStatus.CLOSED]: "bg-gray-100 text-gray-800",
    [ContactStatus.SPAM]: "bg-red-100 text-red-800",
  };
  return colorMap[status] || "bg-gray-100 text-gray-800";
}

/**
 * Helper function to get contact priority color class
 */
export function getContactPriorityColor(priority: ContactPriority): string {
  const colorMap: Record<ContactPriority, string> = {
    [ContactPriority.LOW]: "bg-gray-100 text-gray-800",
    [ContactPriority.NORMAL]: "bg-blue-100 text-blue-800",
    [ContactPriority.HIGH]: "bg-orange-100 text-orange-800",
    [ContactPriority.URGENT]: "bg-red-100 text-red-800",
  };
  return colorMap[priority] || "bg-gray-100 text-gray-800";
}