/**
 * Audit Log Transform Functions
 * Chuyển đổi giữa Entity và DTO
 */

import { AuditLog } from "@prisma/client";
import { AuditLogEntity } from "../../models/audit-log.model";
import { 
  AuditLogResponseDto, 
  CreateAuditLogRequestDto,
  AuditLogStatsDto 
} from "../audit-log.dto";

/**
 * Transform Prisma AuditLog to AuditLogEntity
 */
export function transformPrismaToEntity(prismaAuditLog: AuditLog): AuditLogEntity {
  return {
    id: prismaAuditLog.id,
    adminId: prismaAuditLog.adminId,
    action: prismaAuditLog.action,
    resource: prismaAuditLog.resource,
    resourceId: prismaAuditLog.resourceId || undefined,
    oldValues: prismaAuditLog.oldValues as Record<string, any> || undefined,
    newValues: prismaAuditLog.newValues as Record<string, any> || undefined,
    description: prismaAuditLog.description || undefined,
    ipAddress: prismaAuditLog.ipAddress || undefined,
    userAgent: prismaAuditLog.userAgent || undefined,
    createdAt: prismaAuditLog.createdAt,
    updatedAt: prismaAuditLog.createdAt, // AuditLog doesn't have updatedAt
  };
}

/**
 * Transform AuditLogEntity to AuditLogResponseDto
 */
export function transformEntityToResponseDto(
  entity: AuditLogEntity,
  adminInfo?: { name: string; email: string }
): AuditLogResponseDto {
  return {
    id: entity.id,
    action: entity.action,
    resource: entity.resource,
    resourceId: entity.resourceId,
    oldValues: entity.oldValues,
    newValues: entity.newValues,
    description: entity.description,
    ipAddress: entity.ipAddress,
    userAgent: entity.userAgent,
    adminId: entity.adminId,
    adminName: adminInfo?.name,
    adminEmail: adminInfo?.email,
    createdAt: entity.createdAt.toISOString(),
  };
}

/**
 * Transform Prisma AuditLog with Admin to AuditLogResponseDto
 */
export function transformPrismaWithAdminToResponseDto(
  prismaAuditLog: AuditLog & {
    admin: {
      id: string;
      name: string;
      email: string;
    };
  }
): AuditLogResponseDto {
  return {
    id: prismaAuditLog.id,
    action: prismaAuditLog.action,
    resource: prismaAuditLog.resource,
    resourceId: prismaAuditLog.resourceId || undefined,
    oldValues: prismaAuditLog.oldValues as Record<string, any> || undefined,
    newValues: prismaAuditLog.newValues as Record<string, any> || undefined,
    description: prismaAuditLog.description || undefined,
    ipAddress: prismaAuditLog.ipAddress || undefined,
    userAgent: prismaAuditLog.userAgent || undefined,
    adminId: prismaAuditLog.adminId,
    adminName: prismaAuditLog.admin.name,
    adminEmail: prismaAuditLog.admin.email,
    createdAt: prismaAuditLog.createdAt.toISOString(),
  };
}

/**
 * Transform CreateAuditLogRequestDto to AuditLogEntity creation data
 */
export function transformCreateRequestToEntity(
  dto: CreateAuditLogRequestDto,
  adminId: string
): Omit<AuditLogEntity, "id" | "createdAt" | "updatedAt"> {
  return {
    adminId,
    action: dto.action,
    resource: dto.resource,
    resourceId: dto.resourceId,
    oldValues: dto.oldValues,
    newValues: dto.newValues,
    description: dto.description,
    ipAddress: dto.ipAddress,
    userAgent: dto.userAgent,
  };
}

/**
 * Transform raw stats data to AuditLogStatsDto
 */
export function transformStatsToDto(statsData: {
  totalLogs: number;
  todayLogs: number;
  weekLogs: number;
  monthLogs: number;
  topActions: Array<{ action: string; count: number }>;
  topResources: Array<{ resource: string; count: number }>;
  topAdmins: Array<{ adminId: string; adminName: string; count: number }>;
  recentActivities: AuditLogResponseDto[];
}): AuditLogStatsDto {
  return {
    totalLogs: statsData.totalLogs,
    todayLogs: statsData.todayLogs,
    weekLogs: statsData.weekLogs,
    monthLogs: statsData.monthLogs,
    topActions: statsData.topActions,
    topResources: statsData.topResources,
    topAdmins: statsData.topAdmins,
    recentActivities: statsData.recentActivities,
  };
}

/**
 * Sanitize sensitive data from audit log values
 */
export function sanitizeAuditLogValues(values: Record<string, any>): Record<string, any> {
  const sensitiveFields = ["password", "token", "secret", "key", "apiKey", "accessToken"];
  const sanitized = { ...values };

  sensitiveFields.forEach((field) => {
    if (sanitized[field]) {
      sanitized[field] = "[REDACTED]";
    }
  });

  return sanitized;
}

/**
 * Format audit log action for display
 */
export function formatAuditLogAction(action: string): string {
  const actionLabels: Record<string, string> = {
    CREATE: "Tạo mới",
    UPDATE: "Cập nhật", 
    DELETE: "Xóa",
    LOGIN: "Đăng nhập",
    LOGOUT: "Đăng xuất",
    VIEW: "Xem",
    EXPORT: "Xuất dữ liệu",
    IMPORT: "Nhập dữ liệu",
    APPROVE: "Phê duyệt",
    REJECT: "Từ chối",
    ACTIVATE: "Kích hoạt",
    DEACTIVATE: "Vô hiệu hóa",
  };

  return actionLabels[action.toUpperCase()] || action;
}

/**
 * Format audit log resource for display
 */
export function formatAuditLogResource(resource: string): string {
  const resourceLabels: Record<string, string> = {
    USER: "Người dùng",
    ADMIN_USER: "Quản trị viên",
    PRODUCT: "Sản phẩm",
    CATEGORY: "Danh mục",
    BRAND: "Thương hiệu",
    ORDER: "Đơn hàng",
    POST: "Bài viết",
    PAGE: "Trang",
    SETTING: "Cài đặt",
    NOTIFICATION: "Thông báo",
    CONTACT: "Liên hệ",
    REVIEW: "Đánh giá",
    PROMOTION: "Khuyến mãi",
    INVENTORY: "Kho hàng",
    MEDIA: "Media",
    MENU: "Menu",
  };

  return resourceLabels[resource.toUpperCase()] || resource;
}