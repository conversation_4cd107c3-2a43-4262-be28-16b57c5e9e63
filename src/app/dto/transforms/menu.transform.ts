/**
 * Menu Transform Functions
 * Chuyển đổi giữa Prisma models và DTOs
 */

import { Menu, MenuItem, Prisma } from "@prisma/client";
import {
  MenuDTO,
  MenuItemDTO,
  MenuWithItemsDTO,
  MenuItemWithChildrenDTO,
  CreateMenuDTO,
  UpdateMenuDTO,
  CreateMenuItemDTO,
  UpdateMenuItemDTO,
  MenuItemType,
} from "../menu.dto";

/**
 * Transform Menu từ Prisma model sang DTO
 */
export function transformMenuToDTO(menu: Menu): MenuDTO {
  return {
    id: menu.id,
    name: menu.name,
    location: menu.location,
    description: menu.description || undefined,
    isActive: menu.isActive,
    createdAt: menu.createdAt.toISOString(),
    updatedAt: menu.updatedAt.toISOString(),
  };
}

/**
 * Transform MenuItem từ Prisma model sang DTO
 */
export function transformMenuItemToDTO(menuItem: MenuItem): MenuItemDTO {
  return {
    id: menuItem.id,
    menuId: menuItem.menuId,
    parentId: menuItem.parentId || undefined,
    title: menuItem.title,
    url: menuItem.url || undefined,
    type: menuItem.type as MenuItemType,
    target: menuItem.target || undefined,
    icon: menuItem.icon || undefined,
    cssClass: menuItem.cssClass || undefined,
    order: menuItem.order,
    isActive: menuItem.isActive,
    createdAt: menuItem.createdAt.toISOString(),
    updatedAt: menuItem.updatedAt.toISOString(),
  };
}

/**
 * Transform Menu with Items từ Prisma model sang DTO
 */
export function transformMenuWithItemsToDTO(
  menu: Prisma.MenuGetPayload<{
    include: {
      items: {
        include: {
          children: {
            include: {
              children: true;
            };
          };
        };
      };
    };
  }>
): MenuWithItemsDTO {
  return {
    ...transformMenuToDTO(menu),
    items: transformMenuItemsToTree(menu.items),
  };
}

/**
 * Transform Menu Items thành tree structure
 */
export function transformMenuItemsToTree(
  items: (MenuItem & {
    children?: (MenuItem & {
      children?: MenuItem[];
    })[];
  })[]
): MenuItemWithChildrenDTO[] {
  return items.map((item) => ({
    ...transformMenuItemToDTO(item),
    children: item.children ? transformMenuItemsToTree(item.children) : undefined,
  }));
}

/**
 * Transform CreateMenuDTO sang Prisma input
 */
export function transformCreateMenuDTOToPrisma(
  dto: CreateMenuDTO
): Prisma.MenuCreateInput {
  return {
    name: dto.name,
    location: dto.location,
    description: dto.description,
    isActive: dto.isActive ?? true,
  };
}

/**
 * Transform UpdateMenuDTO sang Prisma input
 */
export function transformUpdateMenuDTOToPrisma(
  dto: UpdateMenuDTO
): Prisma.MenuUpdateInput {
  const updateData: Prisma.MenuUpdateInput = {};

  if (dto.name !== undefined) updateData.name = dto.name;
  if (dto.location !== undefined) updateData.location = dto.location;
  if (dto.description !== undefined) updateData.description = dto.description;
  if (dto.isActive !== undefined) updateData.isActive = dto.isActive;

  return updateData;
}

/**
 * Transform CreateMenuItemDTO sang Prisma input
 */
export function transformCreateMenuItemDTOToPrisma(
  dto: CreateMenuItemDTO
): Prisma.MenuItemCreateInput {
  return {
    title: dto.title,
    url: dto.url,
    type: dto.type || MenuItemType.LINK,
    target: dto.target,
    icon: dto.icon,
    cssClass: dto.cssClass,
    order: dto.order ?? 0,
    isActive: dto.isActive ?? true,
    menu: {
      connect: { id: dto.menuId },
    },
    ...(dto.parentId && {
      parent: {
        connect: { id: dto.parentId },
      },
    }),
  };
}

/**
 * Transform UpdateMenuItemDTO sang Prisma input
 */
export function transformUpdateMenuItemDTOToPrisma(
  dto: UpdateMenuItemDTO
): Prisma.MenuItemUpdateInput {
  const updateData: Prisma.MenuItemUpdateInput = {};

  if (dto.title !== undefined) updateData.title = dto.title;
  if (dto.url !== undefined) updateData.url = dto.url;
  if (dto.type !== undefined) updateData.type = dto.type;
  if (dto.target !== undefined) updateData.target = dto.target;
  if (dto.icon !== undefined) updateData.icon = dto.icon;
  if (dto.cssClass !== undefined) updateData.cssClass = dto.cssClass;
  if (dto.order !== undefined) updateData.order = dto.order;
  if (dto.isActive !== undefined) updateData.isActive = dto.isActive;

  // Handle parent relationship
  if (dto.parentId !== undefined) {
    if (dto.parentId) {
      updateData.parent = { connect: { id: dto.parentId } };
    } else {
      updateData.parent = { disconnect: true };
    }
  }

  return updateData;
}

/**
 * Build menu tree từ flat array
 */
export function buildMenuTree(items: MenuItemDTO[]): MenuItemWithChildrenDTO[] {
  const itemMap = new Map<string, MenuItemWithChildrenDTO>();
  const rootItems: MenuItemWithChildrenDTO[] = [];

  // Create map of all items
  items.forEach((item) => {
    itemMap.set(item.id, { ...item, children: [] });
  });

  // Build tree structure
  items.forEach((item) => {
    const menuItem = itemMap.get(item.id)!;

    if (item.parentId) {
      const parent = itemMap.get(item.parentId);
      if (parent) {
        parent.children!.push(menuItem);
      }
    } else {
      rootItems.push(menuItem);
    }
  });

  // Sort by order
  const sortItems = (items: MenuItemWithChildrenDTO[]) => {
    items.sort((a, b) => a.order - b.order);
    items.forEach((item) => {
      if (item.children && item.children.length > 0) {
        sortItems(item.children);
      }
    });
  };

  sortItems(rootItems);
  return rootItems;
}

/**
 * Flatten menu tree thành array
 */
export function flattenMenuTree(items: MenuItemWithChildrenDTO[]): MenuItemDTO[] {
  const result: MenuItemDTO[] = [];

  const flatten = (items: MenuItemWithChildrenDTO[]) => {
    items.forEach((item) => {
      const { children, ...menuItem } = item;
      result.push(menuItem);
      
      if (children && children.length > 0) {
        flatten(children);
      }
    });
  };

  flatten(items);
  return result;
}

/**
 * Validate menu item depth
 */
export function validateMenuItemDepth(
  parentId: string | undefined,
  items: MenuItemDTO[],
  maxDepth: number = 3
): boolean {
  if (!parentId) return true;

  let depth = 0;
  let currentParentId: string | undefined = parentId;

  while (currentParentId && depth < maxDepth) {
    const parent = items.find((item) => item.id === currentParentId);
    if (!parent) break;

    currentParentId = parent.parentId;
    depth++;
  }

  return depth < maxDepth;
}

/**
 * Get menu item breadcrumb
 */
export function getMenuItemBreadcrumb(
  itemId: string,
  items: MenuItemDTO[]
): MenuItemDTO[] {
  const breadcrumb: MenuItemDTO[] = [];
  let currentId: string | undefined = itemId;

  while (currentId) {
    const item = items.find((item) => item.id === currentId);
    if (!item) break;

    breadcrumb.unshift(item);
    currentId = item.parentId;
  }

  return breadcrumb;
}