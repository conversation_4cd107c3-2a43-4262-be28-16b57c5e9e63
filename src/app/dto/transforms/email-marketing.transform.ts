/**
 * Email Marketing Transform Functions
 * Transform functions giữa Models và DTOs cho Email Marketing
 */

import {
  EmailTemplateEntity,
  EmailTemplateWithRelations,
  EmailLogEntity,
  EmailLogWithRelations,
  SMTPConfigEntity,
} from "@/app/models";

import {
  EmailTemplateDto,
  EmailTemplateWithRelationsDto,
  EmailLogDto,
  EmailLogWithRelationsDto,
  SMTPConfigDto,
  SMTPConfigSafeDto,
} from "@/app/dto";

/**
 * Email Template Transforms
 */
export class EmailTemplateTransform {
  /**
   * Transform EmailTemplateEntity to EmailTemplateDto
   */
  static toDto(entity: EmailTemplateEntity): EmailTemplateDto {
    return {
      id: entity.id,
      name: entity.name,
      subject: entity.subject,
      content: entity.content,
      type: entity.type,
      variables: entity.variables as Record<string, any> | null,
      isActive: entity.isActive,
      isDefault: entity.isDefault,
      createdBy: entity.createdBy,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    };
  }

  /**
   * Transform EmailTemplateWithRelations to EmailTemplateWithRelationsDto
   */
  static toWithRelationsDto(
    entity: EmailTemplateWithRelations
  ): EmailTemplateWithRelationsDto {
    return {
      ...this.toDto(entity),
      _count: entity._count,
    };
  }

  /**
   * Transform array of EmailTemplateEntity to array of EmailTemplateDto
   */
  static toDtoArray(entities: EmailTemplateEntity[]): EmailTemplateDto[] {
    return entities.map((entity) => this.toDto(entity));
  }

  /**
   * Transform array of EmailTemplateWithRelations to array of EmailTemplateWithRelationsDto
   */
  static toWithRelationsDtoArray(
    entities: EmailTemplateWithRelations[]
  ): EmailTemplateWithRelationsDto[] {
    return entities.map((entity) => this.toWithRelationsDto(entity));
  }

  /**
   * Transform Prisma EmailTemplate to EmailTemplateEntity
   */
  static fromPrisma(prismaEntity: any): EmailTemplateEntity {
    return {
      id: prismaEntity.id,
      name: prismaEntity.name,
      subject: prismaEntity.subject,
      content: prismaEntity.content,
      type: prismaEntity.type,
      variables: prismaEntity.variables,
      isActive: prismaEntity.isActive,
      isDefault: prismaEntity.isDefault,
      createdBy: prismaEntity.createdBy,
      createdAt: prismaEntity.createdAt,
      updatedAt: prismaEntity.updatedAt,
    };
  }

  /**
   * Transform Prisma EmailTemplate with relations to EmailTemplateWithRelations
   */
  static fromPrismaWithRelations(
    prismaEntity: any
  ): EmailTemplateWithRelations {
    return {
      ...this.fromPrisma(prismaEntity),
      _count: prismaEntity._count,
    };
  }
}

/**
 * Email Log Transforms
 */
export class EmailLogTransform {
  /**
   * Transform EmailLogEntity to EmailLogDto
   */
  static toDto(entity: EmailLogEntity): EmailLogDto {
    return {
      id: entity.id,
      templateId: entity.templateId,
      recipient: entity.recipient,
      subject: entity.subject,
      content: entity.content,
      status: entity.status,
      error: entity.error,
      sentAt: entity.sentAt?.toISOString() || null,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    };
  }

  /**
   * Transform EmailLogWithRelations to EmailLogWithRelationsDto
   */
  static toWithRelationsDto(
    entity: EmailLogWithRelations
  ): EmailLogWithRelationsDto {
    return {
      ...this.toDto(entity),
      template: entity.template,
    };
  }

  /**
   * Transform array of EmailLogEntity to array of EmailLogDto
   */
  static toDtoArray(entities: EmailLogEntity[]): EmailLogDto[] {
    return entities.map((entity) => this.toDto(entity));
  }

  /**
   * Transform array of EmailLogWithRelations to array of EmailLogWithRelationsDto
   */
  static toWithRelationsDtoArray(
    entities: EmailLogWithRelations[]
  ): EmailLogWithRelationsDto[] {
    return entities.map((entity) => this.toWithRelationsDto(entity));
  }

  /**
   * Transform Prisma EmailLog to EmailLogEntity
   */
  static fromPrisma(prismaEntity: any): EmailLogEntity {
    return {
      id: prismaEntity.id,
      templateId: prismaEntity.templateId,
      recipient: prismaEntity.recipient,
      subject: prismaEntity.subject,
      content: prismaEntity.content,
      status: prismaEntity.status,
      error: prismaEntity.error,
      sentAt: prismaEntity.sentAt,
      createdAt: prismaEntity.createdAt,
      updatedAt: prismaEntity.updatedAt,
    };
  }

  /**
   * Transform Prisma EmailLog with relations to EmailLogWithRelations
   */
  static fromPrismaWithRelations(prismaEntity: any): EmailLogWithRelations {
    return {
      ...this.fromPrisma(prismaEntity),
      template: prismaEntity.template
        ? {
            id: prismaEntity.template.id,
            name: prismaEntity.template.name,
            type: prismaEntity.template.type,
          }
        : null,
    };
  }
}

/**
 * SMTP Config Transforms
 */
export class SMTPConfigTransform {
  /**
   * Transform SMTPConfigEntity to SMTPConfigDto
   */
  static toDto(entity: SMTPConfigEntity): SMTPConfigDto {
    return {
      id: entity.id,
      name: entity.name,
      host: entity.host,
      port: entity.port,
      secure: entity.secure,
      username: entity.username,
      password: entity.password,
      fromName: entity.fromName,
      fromEmail: entity.fromEmail,
      isActive: entity.isActive,
      isDefault: entity.isDefault,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    };
  }

  /**
   * Transform SMTPConfigEntity to SMTPConfigSafeDto (without password)
   */
  static toSafeDto(entity: SMTPConfigEntity): SMTPConfigSafeDto {
    return {
      id: entity.id,
      name: entity.name,
      host: entity.host,
      port: entity.port,
      secure: entity.secure,
      username: entity.username,
      fromName: entity.fromName,
      fromEmail: entity.fromEmail,
      isActive: entity.isActive,
      isDefault: entity.isDefault,
      hasPassword: Boolean(entity.password),
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    };
  }

  /**
   * Transform array of SMTPConfigEntity to array of SMTPConfigDto
   */
  static toDtoArray(entities: SMTPConfigEntity[]): SMTPConfigDto[] {
    return entities.map((entity) => this.toDto(entity));
  }

  /**
   * Transform array of SMTPConfigEntity to array of SMTPConfigSafeDto
   */
  static toSafeDtoArray(entities: SMTPConfigEntity[]): SMTPConfigSafeDto[] {
    return entities.map((entity) => this.toSafeDto(entity));
  }

  /**
   * Transform Prisma SMTPConfig to SMTPConfigEntity
   */
  static fromPrisma(prismaEntity: any): SMTPConfigEntity {
    return {
      id: prismaEntity.id,
      name: prismaEntity.name,
      host: prismaEntity.host,
      port: prismaEntity.port,
      secure: prismaEntity.secure,
      username: prismaEntity.username,
      password: prismaEntity.password,
      fromName: prismaEntity.fromName,
      fromEmail: prismaEntity.fromEmail,
      isActive: prismaEntity.isActive,
      isDefault: prismaEntity.isDefault,
      createdAt: prismaEntity.createdAt,
      updatedAt: prismaEntity.updatedAt,
    };
  }
}

/**
 * Email Marketing Transform Utilities
 */
export class EmailMarketingTransformUtils {
  /**
   * Transform pagination result for Email Templates
   */
  static transformEmailTemplatePaginationResult(result: {
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }) {
    return {
      data: EmailTemplateTransform.toWithRelationsDtoArray(
        result.data.map((item) =>
          EmailTemplateTransform.fromPrismaWithRelations(item)
        )
      ),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  /**
   * Transform pagination result for Email Logs
   */
  static transformEmailLogPaginationResult(result: {
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }) {
    return {
      data: EmailLogTransform.toWithRelationsDtoArray(
        result.data.map((item) =>
          EmailLogTransform.fromPrismaWithRelations(item)
        )
      ),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  /**
   * Transform pagination result for SMTP Configs
   */
  static transformSMTPConfigPaginationResult(result: {
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }) {
    return {
      data: SMTPConfigTransform.toSafeDtoArray(
        result.data.map((item) => SMTPConfigTransform.fromPrisma(item))
      ),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  /**
   * Transform email template variables for frontend
   */
  static transformTemplateVariables(
    variables: Record<string, any> | null
  ): Record<string, any> {
    if (!variables) {
      return {};
    }

    // Ensure all values are properly formatted for frontend
    const transformed: Record<string, any> = {};

    for (const [key, value] of Object.entries(variables)) {
      if (typeof value === "string") {
        transformed[key] = value;
      } else if (typeof value === "object" && value !== null) {
        transformed[key] = JSON.stringify(value);
      } else {
        transformed[key] = String(value);
      }
    }

    return transformed;
  }

  /**
   * Transform template variables for database storage
   */
  static transformTemplateVariablesForStorage(
    variables: Record<string, any>
  ): Record<string, any> {
    const transformed: Record<string, any> = {};

    for (const [key, value] of Object.entries(variables)) {
      if (typeof value === "string") {
        // Try to parse JSON strings
        try {
          const parsed = JSON.parse(value);
          transformed[key] = parsed;
        } catch {
          transformed[key] = value;
        }
      } else {
        transformed[key] = value;
      }
    }

    return transformed;
  }
}
