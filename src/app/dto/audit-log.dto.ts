/**
 * Audit Log DTOs
 * Data Transfer Objects cho Audit Log module
 */

import { z } from "zod";
import { PaginationRequestDto, PaginationResponseDto } from "./common.dto";

/**
 * Audit Log Response DTO
 */
export interface AuditLogResponseDto {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
  adminId: string;
  adminName?: string;
  adminEmail?: string;
  createdAt: string;
}

/**
 * Create Audit Log Request DTO
 */
export interface CreateAuditLogRequestDto {
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Audit Log Filters DTO
 */
export interface AuditLogFiltersDto extends PaginationRequestDto {
  action?: string;
  resource?: string;
  adminId?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  ipAddress?: string;
}

/**
 * Audit Log Stats DTO
 */
export interface AuditLogStatsDto {
  totalLogs: number;
  todayLogs: number;
  weekLogs: number;
  monthLogs: number;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  topResources: Array<{
    resource: string;
    count: number;
  }>;
  topAdmins: Array<{
    adminId: string;
    adminName: string;
    count: number;
  }>;
  recentActivities: AuditLogResponseDto[];
}

/**
 * Audit Log Filter Options DTO
 */
export interface AuditLogFilterOptionsDto {
  actions: string[];
  resources: string[];
  admins: Array<{
    id: string;
    name: string;
    email: string;
  }>;
}

/**
 * Audit Log Export Request DTO
 */
export interface AuditLogExportRequestDto extends AuditLogFiltersDto {
  format: "csv" | "json" | "excel";
}

/**
 * Validation Schemas
 */
export const CreateAuditLogRequestSchema = z.object({
  action: z.string().min(1, "Action is required").max(50, "Action too long"),
  resource: z.string().min(1, "Resource is required").max(50, "Resource too long"),
  resourceId: z.string().optional(),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  description: z.string().max(500, "Description too long").optional(),
  ipAddress: z.string().ip().optional().or(z.literal("unknown")),
  userAgent: z.string().max(500, "User agent too long").optional(),
});

export const AuditLogFiltersSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.enum(["createdAt", "action", "resource", "admin"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  action: z.string().optional(),
  resource: z.string().optional(),
  adminId: z.string().uuid().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  search: z.string().optional(),
  ipAddress: z.string().optional(),
});

export const AuditLogExportRequestSchema = AuditLogFiltersSchema.extend({
  format: z.enum(["csv", "json", "excel"]).default("csv"),
});

/**
 * Type exports
 */
export type CreateAuditLogRequest = z.infer<typeof CreateAuditLogRequestSchema>;
export type AuditLogFilters = z.infer<typeof AuditLogFiltersSchema>;
export type AuditLogExportRequest = z.infer<typeof AuditLogExportRequestSchema>;

/**
 * Validation functions
 */
export const validateCreateAuditLogRequest = (data: any): CreateAuditLogRequest => {
  return CreateAuditLogRequestSchema.parse(data);
};

export const validateAuditLogFilters = (data: any): AuditLogFilters => {
  return AuditLogFiltersSchema.parse(data);
};

export const validateAuditLogExportRequest = (data: any): AuditLogExportRequest => {
  return AuditLogExportRequestSchema.parse(data);
};

/**
 * Audit Log Paginated Response DTO
 */
export type AuditLogPaginatedResponseDto = PaginationResponseDto<AuditLogResponseDto>;