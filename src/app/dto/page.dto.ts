import { z } from 'zod';
import { PageStatus } from '@prisma/client';
import { seoDataSchema } from './common.dto';

// Base Page Schema for common fields
export const basePageSchema = z.object({
  title: z.string().min(2, 'Page title must be at least 2 characters').max(200, 'Page title must not exceed 200 characters').trim(),
  slug: z.string().regex(/^[a-z0-9-]+$/, 'Page slug can only contain lowercase letters, numbers, and hyphens').min(2, 'Page slug must be at least 2 characters').max(100, 'Page slug must not exceed 100 characters').trim(),
  content: z.string().min(10, 'Page content must be at least 10 characters').trim(),
  excerpt: z.string().max(160, 'Excerpt must not exceed 160 characters').optional().nullable(),
  status: z.nativeEnum(PageStatus).default(PageStatus.DRAFT),
  featured: z.boolean().default(false), // Added
  featuredImageId: z.string().uuid().optional().nullable(), // Added
  metaTitle: z.string().max(200).optional().nullable(), // Added
  metaDescription: z.string().max(300).optional().nullable(), // Added
  keywords: z.array(z.string()).default([]), // Changed from nullable() to default([])
  metadata: z.record(z.any()).optional().nullable(),
  authorId: z.string().uuid(), // Added
  viewCount: z.number().int().min(0).default(0), // Added
  mediaId: z.string().uuid().optional().nullable(), // Added
});

// Schema for creating a new page
export const CreatePageDtoSchema = basePageSchema.extend({
  slug: basePageSchema.shape.slug.optional(), // Slug can be optional on creation, will be generated if not provided
  authorId: z.string().uuid(), // Ensure authorId is required for creation
});

export type CreatePageDto = z.infer<typeof CreatePageDtoSchema>;

// Schema for updating an existing page
export const UpdatePageDtoSchema = basePageSchema.partial();

export type UpdatePageDto = z.infer<typeof UpdatePageDtoSchema>;

// Schema for Page response (including id and timestamps)
export const PageDtoSchema = basePageSchema.extend({
  id: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type PageDto = z.infer<typeof PageDtoSchema>;
