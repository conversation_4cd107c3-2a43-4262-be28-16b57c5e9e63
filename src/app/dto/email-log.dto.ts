/**
 * Email Log DTOs
 * Data Transfer Objects cho Email Log
 */

import { z } from "zod";
import { EmailStatus } from "@prisma/client";

/**
 * Email Log DTO Schema
 */
export const EmailLogDtoSchema = z.object({
  id: z.string(),
  templateId: z.string().optional().nullable(),
  recipient: z.string().email(),
  subject: z.string(),
  content: z.string(),
  status: z.nativeEnum(EmailStatus),
  error: z.string().optional().nullable(),
  sentAt: z.string().optional().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Email Log DTO
 */
export type EmailLogDto = z.infer<typeof EmailLogDtoSchema>;

/**
 * Email Log with Relations DTO Schema
 */
export const EmailLogWithRelationsDtoSchema = EmailLogDtoSchema.extend({
  template: z.object({
    id: z.string(),
    name: z.string(),
    type: z.string(),
  }).optional().nullable(),
});

/**
 * Email Log with Relations DTO
 */
export type EmailLogWithRelationsDto = z.infer<typeof EmailLogWithRelationsDtoSchema>;

/**
 * Create Email Log DTO Schema
 */
export const CreateEmailLogDtoSchema = z.object({
  templateId: z.string().optional(),
  recipient: z.string().email("Email không hợp lệ"),
  subject: z.string()
    .min(1, "Tiêu đề email là bắt buộc")
    .max(255, "Tiêu đề email không được quá 255 ký tự"),
  content: z.string().min(1, "Nội dung email là bắt buộc"),
  status: z.nativeEnum(EmailStatus).default(EmailStatus.PENDING),
  error: z.string().optional(),
  sentAt: z.string().optional(),
});

/**
 * Create Email Log DTO
 */
export type CreateEmailLogDto = z.infer<typeof CreateEmailLogDtoSchema>;

/**
 * Update Email Log DTO Schema
 */
export const UpdateEmailLogDtoSchema = z.object({
  status: z.nativeEnum(EmailStatus).optional(),
  error: z.string().optional(),
  sentAt: z.string().optional(),
});

/**
 * Update Email Log DTO
 */
export type UpdateEmailLogDto = z.infer<typeof UpdateEmailLogDtoSchema>;

/**
 * Email Log Search DTO Schema
 */
export const EmailLogSearchDtoSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  templateId: z.string().optional(),
  recipient: z.string().optional(),
  status: z.nativeEnum(EmailStatus).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["recipient", "subject", "status", "sentAt", "createdAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Email Log Search DTO
 */
export type EmailLogSearchDto = z.infer<typeof EmailLogSearchDtoSchema>;

/**
 * Email Log Stats DTO
 */
export interface EmailLogStatsDto {
  total: number;
  sent: number;
  failed: number;
  pending: number;
  bounced: number;
  successRate: number;
  failureRate: number;
  recentActivity: EmailLogDto[];
  topFailureReasons: {
    error: string;
    count: number;
  }[];
}

/**
 * Email Log Retry DTO Schema
 */
export const EmailLogRetryDtoSchema = z.object({
  logIds: z.array(z.string()).min(1, "Ít nhất một email phải được chọn"),
});

/**
 * Email Log Retry DTO
 */
export type EmailLogRetryDto = z.infer<typeof EmailLogRetryDtoSchema>;

/**
 * Email Log Export DTO Schema
 */
export const EmailLogExportDtoSchema = z.object({
  format: z.enum(["csv", "excel"]).default("csv"),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  status: z.nativeEnum(EmailStatus).optional(),
  templateId: z.string().optional(),
});

/**
 * Email Log Export DTO
 */
export type EmailLogExportDto = z.infer<typeof EmailLogExportDtoSchema>;

/**
 * Email Status Display Names
 */
export const EMAIL_STATUS_DISPLAY_NAMES: Record<EmailStatus, string> = {
  [EmailStatus.PENDING]: "Đang chờ",
  [EmailStatus.SENT]: "Đã gửi",
  [EmailStatus.FAILED]: "Thất bại",
  [EmailStatus.BOUNCED]: "Bị trả về",
};

/**
 * Email Status Colors
 */
export const EMAIL_STATUS_COLORS: Record<EmailStatus, string> = {
  [EmailStatus.PENDING]: "yellow",
  [EmailStatus.SENT]: "green",
  [EmailStatus.FAILED]: "red",
  [EmailStatus.BOUNCED]: "orange",
};

/**
 * Email Status Icons
 */
export const EMAIL_STATUS_ICONS: Record<EmailStatus, string> = {
  [EmailStatus.PENDING]: "Clock",
  [EmailStatus.SENT]: "CheckCircle",
  [EmailStatus.FAILED]: "XCircle",
  [EmailStatus.BOUNCED]: "AlertCircle",
};

/**
 * Helper functions
 */
export const EmailLogHelpers = {
  /**
   * Get display name for email status
   */
  getStatusDisplayName(status: EmailStatus): string {
    return EMAIL_STATUS_DISPLAY_NAMES[status] || status;
  },

  /**
   * Get color for email status
   */
  getStatusColor(status: EmailStatus): string {
    return EMAIL_STATUS_COLORS[status] || "gray";
  },

  /**
   * Get icon for email status
   */
  getStatusIcon(status: EmailStatus): string {
    return EMAIL_STATUS_ICONS[status] || "Circle";
  },

  /**
   * Check if email can be retried
   */
  canRetry(status: EmailStatus): boolean {
    return status === EmailStatus.FAILED || status === EmailStatus.PENDING;
  },

  /**
   * Check if email is in final status
   */
  isFinalStatus(status: EmailStatus): boolean {
    return status === EmailStatus.SENT || status === EmailStatus.BOUNCED;
  },

  /**
   * Get status priority for sorting
   */
  getStatusPriority(status: EmailStatus): number {
    switch (status) {
      case EmailStatus.FAILED:
        return 1;
      case EmailStatus.PENDING:
        return 2;
      case EmailStatus.BOUNCED:
        return 3;
      case EmailStatus.SENT:
        return 4;
      default:
        return 5;
    }
  },

  /**
   * Format email content for display
   */
  formatContentPreview(content: string, maxLength: number = 100): string {
    // Remove HTML tags
    const textContent = content.replace(/<[^>]*>/g, '');
    
    if (textContent.length <= maxLength) {
      return textContent;
    }
    
    return textContent.substring(0, maxLength) + '...';
  },

  /**
   * Calculate success rate
   */
  calculateSuccessRate(stats: EmailLogStatsDto): number {
    if (stats.total === 0) return 0;
    return Math.round((stats.sent / stats.total) * 100 * 100) / 100;
  },

  /**
   * Calculate failure rate
   */
  calculateFailureRate(stats: EmailLogStatsDto): number {
    if (stats.total === 0) return 0;
    return Math.round(((stats.failed + stats.bounced) / stats.total) * 100 * 100) / 100;
  },

  /**
   * Get status badge variant
   */
  getStatusBadgeVariant(status: EmailStatus): "default" | "secondary" | "destructive" | "outline" {
    switch (status) {
      case EmailStatus.SENT:
        return "default";
      case EmailStatus.PENDING:
        return "secondary";
      case EmailStatus.FAILED:
      case EmailStatus.BOUNCED:
        return "destructive";
      default:
        return "outline";
    }
  },
};
