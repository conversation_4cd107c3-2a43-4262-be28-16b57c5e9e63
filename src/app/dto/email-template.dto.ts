/**
 * Email Template DTOs
 * Data Transfer Objects cho Email Template
 */

import { z } from "zod";
import { EmailTemplateType } from "@prisma/client";

/**
 * Email Template DTO Schema
 */
export const EmailTemplateDtoSchema = z.object({
  id: z.string(),
  name: z.string(),
  subject: z.string(),
  content: z.string(),
  type: z.nativeEnum(EmailTemplateType),
  variables: z.record(z.any()).optional().nullable(),
  isActive: z.boolean(),
  isDefault: z.boolean(),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Email Template DTO
 */
export type EmailTemplateDto = z.infer<typeof EmailTemplateDtoSchema>;

/**
 * Email Template with Relations DTO Schema
 */
export const EmailTemplateWithRelationsDtoSchema = EmailTemplateDtoSchema.extend({
  _count: z.object({
    emailLogs: z.number(),
  }).optional(),
});

/**
 * Email Template with Relations DTO
 */
export type EmailTemplateWithRelationsDto = z.infer<typeof EmailTemplateWithRelationsDtoSchema>;

/**
 * Create Email Template DTO Schema
 */
export const CreateEmailTemplateDtoSchema = z.object({
  name: z.string()
    .min(1, "Tên template là bắt buộc")
    .max(255, "Tên template không được quá 255 ký tự"),
  subject: z.string()
    .min(1, "Tiêu đề email là bắt buộc")
    .max(255, "Tiêu đề email không được quá 255 ký tự"),
  content: z.string()
    .min(1, "Nội dung email là bắt buộc"),
  type: z.nativeEnum(EmailTemplateType),
  variables: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
  createdBy: z.string().min(1, "Người tạo là bắt buộc"),
});

/**
 * Create Email Template DTO
 */
export type CreateEmailTemplateDto = z.infer<typeof CreateEmailTemplateDtoSchema>;

/**
 * Update Email Template DTO Schema
 */
export const UpdateEmailTemplateDtoSchema = z.object({
  name: z.string()
    .min(1, "Tên template là bắt buộc")
    .max(255, "Tên template không được quá 255 ký tự")
    .optional(),
  subject: z.string()
    .min(1, "Tiêu đề email là bắt buộc")
    .max(255, "Tiêu đề email không được quá 255 ký tự")
    .optional(),
  content: z.string()
    .min(1, "Nội dung email là bắt buộc")
    .optional(),
  type: z.nativeEnum(EmailTemplateType).optional(),
  variables: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
});

/**
 * Update Email Template DTO
 */
export type UpdateEmailTemplateDto = z.infer<typeof UpdateEmailTemplateDtoSchema>;

/**
 * Email Template Search DTO Schema
 */
export const EmailTemplateSearchDtoSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  type: z.nativeEnum(EmailTemplateType).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  createdBy: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["name", "subject", "type", "createdAt", "updatedAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Email Template Search DTO
 */
export type EmailTemplateSearchDto = z.infer<typeof EmailTemplateSearchDtoSchema>;

/**
 * Email Template Type Info DTO
 */
export interface EmailTemplateTypeInfoDto {
  type: EmailTemplateType;
  name: string;
  description: string;
  defaultVariables: Record<string, any>;
  hasDefault: boolean;
}

/**
 * Email Template Preview DTO Schema
 */
export const EmailTemplatePreviewDtoSchema = z.object({
  templateId: z.string(),
  variables: z.record(z.any()).optional(),
  format: z.enum(["html", "text"]).default("html"),
});

/**
 * Email Template Preview DTO
 */
export type EmailTemplatePreviewDto = z.infer<typeof EmailTemplatePreviewDtoSchema>;

/**
 * Email Template Preview Result DTO
 */
export interface EmailTemplatePreviewResultDto {
  subject: string;
  content: string;
  format: "html" | "text";
}

/**
 * Email Template Validation Result DTO
 */
export interface EmailTemplateValidationResultDto {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Email Template Stats DTO
 */
export interface EmailTemplateStatsDto {
  totalTemplates: number;
  activeTemplates: number;
  templatesByType: Record<EmailTemplateType, number>;
  mostUsedTemplate: {
    id: string;
    name: string;
    usageCount: number;
  } | null;
  recentlyCreated: EmailTemplateDto[];
}

/**
 * Bulk Email Template Operation DTO Schema
 */
export const BulkEmailTemplateOperationDtoSchema = z.object({
  templateIds: z.array(z.string()).min(1, "Ít nhất một template phải được chọn"),
  operation: z.enum(["activate", "deactivate", "delete"]),
});

/**
 * Bulk Email Template Operation DTO
 */
export type BulkEmailTemplateOperationDto = z.infer<typeof BulkEmailTemplateOperationDtoSchema>;

/**
 * Email Template Type Display Names
 */
export const EMAIL_TEMPLATE_TYPE_DISPLAY_NAMES: Record<EmailTemplateType, string> = {
  [EmailTemplateType.WELCOME]: "Chào mừng",
  [EmailTemplateType.ORDER_CONFIRMATION]: "Xác nhận đơn hàng",
  [EmailTemplateType.ORDER_SHIPPED]: "Đơn hàng đã gửi",
  [EmailTemplateType.ORDER_DELIVERED]: "Đơn hàng đã giao",
  [EmailTemplateType.PASSWORD_RESET]: "Đặt lại mật khẩu",
  [EmailTemplateType.NEWSLETTER]: "Bản tin",
  [EmailTemplateType.PROMOTION]: "Khuyến mãi",
  [EmailTemplateType.CUSTOM]: "Tùy chỉnh",
};

/**
 * Email Template Type Descriptions
 */
export const EMAIL_TEMPLATE_TYPE_DESCRIPTIONS: Record<EmailTemplateType, string> = {
  [EmailTemplateType.WELCOME]: "Email gửi cho khách hàng mới đăng ký",
  [EmailTemplateType.ORDER_CONFIRMATION]: "Email xác nhận đơn hàng sau khi đặt hàng",
  [EmailTemplateType.ORDER_SHIPPED]: "Email thông báo đơn hàng đã được gửi",
  [EmailTemplateType.ORDER_DELIVERED]: "Email thông báo đơn hàng đã được giao",
  [EmailTemplateType.PASSWORD_RESET]: "Email đặt lại mật khẩu",
  [EmailTemplateType.NEWSLETTER]: "Email bản tin định kỳ",
  [EmailTemplateType.PROMOTION]: "Email khuyến mãi và ưu đãi",
  [EmailTemplateType.CUSTOM]: "Email tùy chỉnh cho mục đích khác",
};

/**
 * Helper functions
 */
export const EmailTemplateHelpers = {
  /**
   * Get display name for template type
   */
  getTypeDisplayName(type: EmailTemplateType): string {
    return EMAIL_TEMPLATE_TYPE_DISPLAY_NAMES[type] || type;
  },

  /**
   * Get description for template type
   */
  getTypeDescription(type: EmailTemplateType): string {
    return EMAIL_TEMPLATE_TYPE_DESCRIPTIONS[type] || "";
  },

  /**
   * Get all template types with info
   */
  getAllTypeInfo(): EmailTemplateTypeInfoDto[] {
    return Object.values(EmailTemplateType).map(type => ({
      type,
      name: EMAIL_TEMPLATE_TYPE_DISPLAY_NAMES[type],
      description: EMAIL_TEMPLATE_TYPE_DESCRIPTIONS[type],
      defaultVariables: {}, // Will be populated by business rules
      hasDefault: false, // Will be determined by repository
    }));
  },

  /**
   * Validate template variables
   */
  validateTemplateVariables(content: string, variables: Record<string, any>): string[] {
    const errors: string[] = [];
    
    if (!variables) {
      return errors;
    }

    // Extract variables from content ({{variableName}} format)
    const contentVariables = content.match(/\{\{([^}]+)\}\}/g) || [];
    const contentVarNames = contentVariables.map(v => 
      v.replace(/\{\{|\}\}/g, '').trim()
    );

    // Check if all content variables are defined
    for (const varName of contentVarNames) {
      if (!variables.hasOwnProperty(varName)) {
        errors.push(`Biến '${varName}' được sử dụng trong nội dung nhưng chưa được định nghĩa`);
      }
    }

    return errors;
  },
};
