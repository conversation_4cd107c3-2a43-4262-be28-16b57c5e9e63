/**
 * Wishlist DTOs
 * Data Transfer Objects cho Wishlist
 */

import { z } from "zod";

/**
 * Wishlist Item Response DTO
 */
export interface WishlistItemResponseDto {
  id: string;
  userId: string;
  productId: string;
  createdAt: string;
  updatedAt: string;
  product?: {
    id: string;
    name: string;
    slug: string;
    price: number;
    salePrice?: number;
    image?: string;
    status: string;
    brand?: {
      id: string;
      name: string;
      slug: string;
    };
    category?: {
      id: string;
      name: string;
      slug: string;
    };
  };
}

/**
 * Add to Wishlist Request DTO
 */
export interface AddToWishlistRequestDto {
  productId: string;
}

/**
 * Remove from Wishlist Request DTO
 */
export interface RemoveFromWishlistRequestDto {
  productId: string;
}

/**
 * Toggle Wishlist Request DTO
 */
export interface ToggleWishlistRequestDto {
  productId: string;
}

/**
 * Toggle Wishlist Response DTO
 */
export interface ToggleWishlistResponseDto {
  action: "added" | "removed";
  item?: WishlistItemResponseDto;
}

/**
 * Check Wishlist Response DTO
 */
export interface CheckWishlistResponseDto {
  inWishlist: boolean;
}

/**
 * Wishlist Summary Response DTO
 */
export interface WishlistSummaryResponseDto {
  count: number;
  items: WishlistItemResponseDto[];
}

/**
 * Validation Schemas
 */
export const AddToWishlistRequestSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
});

export const RemoveFromWishlistRequestSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
});

export const ToggleWishlistRequestSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
});

/**
 * Validation functions
 */
export const validateAddToWishlist = (data: any): AddToWishlistRequestDto => {
  return AddToWishlistRequestSchema.parse(data);
};

export const validateRemoveFromWishlist = (
  data: any
): RemoveFromWishlistRequestDto => {
  return RemoveFromWishlistRequestSchema.parse(data);
};

export const validateToggleWishlist = (data: any): ToggleWishlistRequestDto => {
  return ToggleWishlistRequestSchema.parse(data);
};
