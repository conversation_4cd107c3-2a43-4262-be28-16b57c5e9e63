/**
 * SMTP Config DTOs
 * Data Transfer Objects cho SMTP Configuration
 */

import { z } from "zod";

/**
 * SMTP Config DTO Schema
 */
export const SMTPConfigDtoSchema = z.object({
  id: z.string(),
  name: z.string(),
  host: z.string(),
  port: z.number(),
  secure: z.boolean(),
  username: z.string(),
  password: z.string(),
  fromName: z.string(),
  fromEmail: z.string().email(),
  isActive: z.boolean(),
  isDefault: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * SMTP Config DTO
 */
export type SMTPConfigDto = z.infer<typeof SMTPConfigDtoSchema>;

/**
 * SMTP Config Safe DTO Schema (without password)
 */
export const SMTPConfigSafeDtoSchema = SMTPConfigDtoSchema.omit({ password: true }).extend({
  hasPassword: z.boolean(),
});

/**
 * SMTP Config Safe DTO (without password)
 */
export type SMTPConfigSafeDto = z.infer<typeof SMTPConfigSafeDtoSchema>;

/**
 * Create SMTP Config DTO Schema
 */
export const CreateSMTPConfigDtoSchema = z.object({
  name: z.string()
    .min(1, "Tên cấu hình là bắt buộc")
    .max(255, "Tên cấu hình không được quá 255 ký tự"),
  host: z.string()
    .min(1, "SMTP host là bắt buộc")
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/, 
           "SMTP host không hợp lệ"),
  port: z.number()
    .min(1, "Port phải lớn hơn 0")
    .max(65535, "Port phải nhỏ hơn 65536"),
  secure: z.boolean().default(true),
  username: z.string().min(1, "Username là bắt buộc"),
  password: z.string().min(1, "Password là bắt buộc"),
  fromName: z.string()
    .min(1, "Tên người gửi là bắt buộc")
    .max(255, "Tên người gửi không được quá 255 ký tự"),
  fromEmail: z.string().email("Email người gửi không hợp lệ"),
  isActive: z.boolean().default(false),
  isDefault: z.boolean().default(false),
});

/**
 * Create SMTP Config DTO
 */
export type CreateSMTPConfigDto = z.infer<typeof CreateSMTPConfigDtoSchema>;

/**
 * Update SMTP Config DTO Schema
 */
export const UpdateSMTPConfigDtoSchema = z.object({
  name: z.string()
    .min(1, "Tên cấu hình là bắt buộc")
    .max(255, "Tên cấu hình không được quá 255 ký tự")
    .optional(),
  host: z.string()
    .min(1, "SMTP host là bắt buộc")
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/, 
           "SMTP host không hợp lệ")
    .optional(),
  port: z.number()
    .min(1, "Port phải lớn hơn 0")
    .max(65535, "Port phải nhỏ hơn 65536")
    .optional(),
  secure: z.boolean().optional(),
  username: z.string().min(1, "Username là bắt buộc").optional(),
  password: z.string().min(1, "Password là bắt buộc").optional(),
  fromName: z.string()
    .min(1, "Tên người gửi là bắt buộc")
    .max(255, "Tên người gửi không được quá 255 ký tự")
    .optional(),
  fromEmail: z.string().email("Email người gửi không hợp lệ").optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
});

/**
 * Update SMTP Config DTO
 */
export type UpdateSMTPConfigDto = z.infer<typeof UpdateSMTPConfigDtoSchema>;

/**
 * SMTP Config Search DTO Schema
 */
export const SMTPConfigSearchDtoSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["name", "host", "fromEmail", "createdAt", "updatedAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * SMTP Config Search DTO
 */
export type SMTPConfigSearchDto = z.infer<typeof SMTPConfigSearchDtoSchema>;

/**
 * SMTP Test DTO Schema
 */
export const SMTPTestDtoSchema = z.object({
  configId: z.string().optional(),
  testEmail: z.string().email("Email test không hợp lệ"),
  testName: z.string().default("Test User"),
});

/**
 * SMTP Test DTO
 */
export type SMTPTestDto = z.infer<typeof SMTPTestDtoSchema>;

/**
 * SMTP Test Result DTO
 */
export interface SMTPTestResultDto {
  success: boolean;
  message: string;
  error?: string;
  responseTime?: number;
  configUsed?: {
    id: string;
    name: string;
    host: string;
    port: number;
  };
}

/**
 * SMTP Provider Template DTO
 */
export interface SMTPProviderTemplateDto {
  provider: string;
  name: string;
  host: string;
  port: number;
  secure: boolean;
  username?: string;
  description: string;
  setupInstructions: string[];
}

/**
 * SMTP Config Stats DTO
 */
export interface SMTPConfigStatsDto {
  totalConfigs: number;
  activeConfigs: number;
  defaultConfig: SMTPConfigSafeDto | null;
  recentlyUsed: SMTPConfigSafeDto[];
  connectionStatus: {
    configId: string;
    name: string;
    connected: boolean;
    lastTested?: string;
    error?: string;
  }[];
}

/**
 * Bulk SMTP Config Operation DTO Schema
 */
export const BulkSMTPConfigOperationDtoSchema = z.object({
  configIds: z.array(z.string()).min(1, "Ít nhất một cấu hình phải được chọn"),
  operation: z.enum(["activate", "deactivate", "delete", "test"]),
});

/**
 * Bulk SMTP Config Operation DTO
 */
export type BulkSMTPConfigOperationDto = z.infer<typeof BulkSMTPConfigOperationDtoSchema>;

/**
 * Common SMTP Providers
 */
export const SMTP_PROVIDERS: SMTPProviderTemplateDto[] = [
  {
    provider: "gmail",
    name: "Gmail",
    host: "smtp.gmail.com",
    port: 587,
    secure: true,
    description: "Google Gmail SMTP",
    setupInstructions: [
      "Bật xác thực 2 bước cho tài khoản Gmail",
      "Tạo App Password trong cài đặt bảo mật",
      "Sử dụng App Password thay vì mật khẩu thường",
    ],
  },
  {
    provider: "outlook",
    name: "Outlook",
    host: "smtp-mail.outlook.com",
    port: 587,
    secure: true,
    description: "Microsoft Outlook SMTP",
    setupInstructions: [
      "Sử dụng email và mật khẩu Outlook",
      "Đảm bảo tài khoản không bị khóa",
    ],
  },
  {
    provider: "sendgrid",
    name: "SendGrid",
    host: "smtp.sendgrid.net",
    port: 587,
    secure: true,
    username: "apikey",
    description: "SendGrid Email Service",
    setupInstructions: [
      "Tạo tài khoản SendGrid",
      "Tạo API Key trong dashboard",
      "Username luôn là 'apikey'",
      "Password là API Key vừa tạo",
    ],
  },
  {
    provider: "mailgun",
    name: "Mailgun",
    host: "smtp.mailgun.org",
    port: 587,
    secure: true,
    description: "Mailgun Email Service",
    setupInstructions: [
      "Tạo tài khoản Mailgun",
      "Xác thực domain",
      "Lấy SMTP credentials từ dashboard",
    ],
  },
  {
    provider: "ses",
    name: "Amazon SES",
    host: "email-smtp.us-east-1.amazonaws.com",
    port: 587,
    secure: true,
    description: "Amazon Simple Email Service",
    setupInstructions: [
      "Tạo tài khoản AWS",
      "Kích hoạt Amazon SES",
      "Tạo SMTP credentials",
      "Xác thực email/domain",
    ],
  },
];

/**
 * Helper functions
 */
export const SMTPConfigHelpers = {
  /**
   * Get provider template by name
   */
  getProviderTemplate(provider: string): SMTPProviderTemplateDto | null {
    return SMTP_PROVIDERS.find(p => p.provider === provider.toLowerCase()) || null;
  },

  /**
   * Get all provider templates
   */
  getAllProviderTemplates(): SMTPProviderTemplateDto[] {
    return SMTP_PROVIDERS;
  },

  /**
   * Validate SMTP configuration
   */
  validateConfig(config: CreateSMTPConfigDto): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Additional validation beyond Zod schema
    if (config.port === 25 && config.secure) {
      errors.push("Port 25 thường không sử dụng SSL/TLS");
    }

    if (config.port === 465 && !config.secure) {
      errors.push("Port 465 yêu cầu SSL/TLS");
    }

    if (config.port === 587 && !config.secure) {
      errors.push("Port 587 nên sử dụng STARTTLS");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  },

  /**
   * Get recommended port settings
   */
  getRecommendedPorts(): { port: number; description: string; secure: boolean }[] {
    return [
      { port: 25, description: "SMTP (Không mã hóa)", secure: false },
      { port: 587, description: "SMTP (STARTTLS)", secure: true },
      { port: 465, description: "SMTPS (SSL/TLS)", secure: true },
      { port: 2525, description: "SMTP thay thế", secure: false },
    ];
  },

  /**
   * Mask password for display
   */
  maskPassword(password: string): string {
    if (password.length <= 4) {
      return "*".repeat(password.length);
    }
    return password.substring(0, 2) + "*".repeat(password.length - 4) + password.substring(password.length - 2);
  },

  /**
   * Check if config is complete
   */
  isConfigComplete(config: Partial<CreateSMTPConfigDto>): boolean {
    const required = ['name', 'host', 'port', 'username', 'password', 'fromName', 'fromEmail'];
    return required.every(field => config[field as keyof CreateSMTPConfigDto] !== undefined);
  },
};
