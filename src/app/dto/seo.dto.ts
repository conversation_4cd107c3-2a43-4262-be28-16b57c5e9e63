/**
 * SEO DTO
 * Data Transfer Objects cho SEO module
 */

import { z } from "zod";

/**
 * SEO Settings DTO
 */
export interface SEOSettingsDTO {
  id: string;
  siteName?: string;
  siteDescription?: string;
  siteKeywords: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: Record<string, any>;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Page SEO DTO
 */
export interface PageSEODTO {
  id: string;
  path: string;
  title?: string;
  description?: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  canonical?: string;
  noindex: boolean;
  nofollow: boolean;
  priority?: number;
  changefreq?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create SEO Settings DTO
 */
export interface CreateSEOSettingsDTO {
  siteName?: string;
  siteDescription?: string;
  siteKeywords?: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled?: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: Record<string, any>;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
}

/**
 * Update SEO Settings DTO
 */
export interface UpdateSEOSettingsDTO extends Partial<CreateSEOSettingsDTO> {}

/**
 * Create Page SEO DTO
 */
export interface CreatePageSEODTO {
  path: string;
  title?: string;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  priority?: number;
  changefreq?: string;
  isActive?: boolean;
}

/**
 * Update Page SEO DTO
 */
export interface UpdatePageSEODTO extends Partial<CreatePageSEODTO> {}

/**
 * SEO Search Filters DTO
 */
export interface SEOSearchFiltersDTO {
  search?: string;
  path?: string;
  isActive?: boolean;
  noindex?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * SEO Stats DTO
 */
export interface SEOStatsDTO {
  totalPages: number;
  activePages: number;
  inactivePages: number;
  noindexPages: number;
  nofollowPages: number;
  pagesWithoutTitle: number;
  pagesWithoutDescription: number;
}

/**
 * Zod Validation Schemas
 */

const sitemapFrequencyEnum = z.enum(["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"]);
const twitterCardEnum = z.enum(["summary", "summary_large_image", "app", "player"]);

export const CreateSEOSettingsSchema = z.object({
  siteName: z.string().max(100, "Site name must not exceed 100 characters").optional(),
  siteDescription: z.string().max(500, "Site description must not exceed 500 characters").optional(),
  siteKeywords: z.array(z.string()).max(20, "Maximum 20 site keywords allowed").optional().default([]),
  defaultTitle: z.string().max(60, "Default title should not exceed 60 characters").optional(),
  titleTemplate: z.string().max(100, "Title template must not exceed 100 characters").optional(),
  defaultDescription: z.string().max(160, "Default description should not exceed 160 characters").optional(),
  defaultImage: z.string().url("Invalid image URL").optional(),
  ogSiteName: z.string().max(100, "OG site name must not exceed 100 characters").optional(),
  ogType: z.string().max(50, "OG type must not exceed 50 characters").optional(),
  twitterSite: z.string().max(50, "Twitter site must not exceed 50 characters").optional(),
  twitterCreator: z.string().max(50, "Twitter creator must not exceed 50 characters").optional(),
  robotsTxt: z.string().optional(),
  sitemapEnabled: z.boolean().optional().default(true),
  sitemapFrequency: sitemapFrequencyEnum.optional().default("weekly"),
  googleAnalyticsId: z.string().max(50, "Google Analytics ID must not exceed 50 characters").optional(),
  googleTagManagerId: z.string().max(50, "Google Tag Manager ID must not exceed 50 characters").optional(),
  facebookPixelId: z.string().max(50, "Facebook Pixel ID must not exceed 50 characters").optional(),
  organizationName: z.string().max(100, "Organization name must not exceed 100 characters").optional(),
  organizationLogo: z.string().url("Invalid logo URL").optional(),
  organizationType: z.string().max(50, "Organization type must not exceed 50 characters").optional(),
  contactEmail: z.string().email("Invalid email format").optional(),
  contactPhone: z.string().max(20, "Contact phone must not exceed 20 characters").optional(),
  address: z.record(z.any()).optional(),
  googleSiteVerification: z.string().max(100, "Google site verification must not exceed 100 characters").optional(),
  bingSiteVerification: z.string().max(100, "Bing site verification must not exceed 100 characters").optional(),
});

export const UpdateSEOSettingsSchema = CreateSEOSettingsSchema.partial();

export const CreatePageSEOSchema = z.object({
  path: z.string().min(1, "Page path is required").regex(/^\//, "Page path must start with /"),
  title: z.string().max(60, "Title should not exceed 60 characters").optional(),
  description: z.string().max(160, "Description should not exceed 160 characters").optional(),
  keywords: z.array(z.string()).max(10, "Maximum 10 keywords allowed").optional().default([]),
  ogTitle: z.string().max(60, "OG title should not exceed 60 characters").optional(),
  ogDescription: z.string().max(160, "OG description should not exceed 160 characters").optional(),
  ogImage: z.string().url("Invalid OG image URL").optional(),
  ogType: z.string().max(50, "OG type must not exceed 50 characters").optional(),
  twitterTitle: z.string().max(60, "Twitter title should not exceed 60 characters").optional(),
  twitterDescription: z.string().max(160, "Twitter description should not exceed 160 characters").optional(),
  twitterImage: z.string().url("Invalid Twitter image URL").optional(),
  twitterCard: twitterCardEnum.optional().default("summary"),
  canonical: z.string().url("Invalid canonical URL").optional(),
  noindex: z.boolean().optional().default(false),
  nofollow: z.boolean().optional().default(false),
  priority: z.number().min(0, "Priority must be at least 0").max(1, "Priority must be at most 1").optional().default(0.5),
  changefreq: sitemapFrequencyEnum.optional().default("weekly"),
  isActive: z.boolean().optional().default(true),
});

export const UpdatePageSEOSchema = CreatePageSEOSchema.partial().omit({ path: true });

export const SEOSearchFiltersSchema = z.object({
  search: z.string().optional(),
  path: z.string().optional(),
  isActive: z.boolean().optional(),
  noindex: z.boolean().optional(),
  page: z.number().int().min(1, "Page must be at least 1").optional().default(1),
  limit: z.number().int().min(1, "Limit must be at least 1").max(100, "Limit must not exceed 100").optional().default(20),
  sortBy: z.string().optional().default("path"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("asc"),
});

/**
 * Type exports for validation
 */
export type CreateSEOSettingsInput = z.infer<typeof CreateSEOSettingsSchema>;
export type UpdateSEOSettingsInput = z.infer<typeof UpdateSEOSettingsSchema>;
export type CreatePageSEOInput = z.infer<typeof CreatePageSEOSchema>;
export type UpdatePageSEOInput = z.infer<typeof UpdatePageSEOSchema>;
export type SEOSearchFiltersInput = z.infer<typeof SEOSearchFiltersSchema>;