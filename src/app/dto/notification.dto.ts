/**
 * Notification DTOs
 * Data Transfer Objects cho Admin Notification system
 */

import { z } from "zod";

/**
 * Notification Type Enum - phù hợp với Prisma schema
 */
export enum NotificationTypeDto {
  INFO = "INFO",
  SUCCESS = "SUCCESS", 
  WARNING = "WARNING",
  ERROR = "ERROR",
  SYSTEM = "SYSTEM",
}

/**
 * Notification Priority Enum - phù hợp với Prisma schema
 */
export enum NotificationPriorityDto {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH", 
  URGENT = "URGENT",
}

/**
 * Notification Target Enum - phù hợp với Prisma schema
 */
export enum NotificationTargetDto {
  ALL_ADMINS = "ALL_ADMINS",
  SPECIFIC_ADMIN = "SPECIFIC_ADMIN",
  ROLE_ADMIN = "ROLE_ADMIN",
  ROLE_MODERATOR = "ROLE_MODERATOR",
}

/**
 * Admin User DTO for notification relations
 */
export interface AdminUserDto {
  id: string;
  name: string;
  email: string;
}

/**
 * Notification Response DTO
 */
export interface NotificationResponseDto {
  id: string;
  title: string;
  message: string;
  type: NotificationTypeDto;
  priority: NotificationPriorityDto;
  targetType: NotificationTargetDto;
  targetId?: string;
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
  creator?: AdminUserDto;
  target?: AdminUserDto;
}

/**
 * Create Notification Request DTO
 */
export interface CreateNotificationRequestDto {
  title: string;
  message: string;
  type?: NotificationTypeDto;
  priority?: NotificationPriorityDto;
  targetType?: NotificationTargetDto;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
}

/**
 * Update Notification Request DTO
 */
export interface UpdateNotificationRequestDto {
  title?: string;
  message?: string;
  type?: NotificationTypeDto;
  priority?: NotificationPriorityDto;
  targetType?: NotificationTargetDto;
  targetId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
  isRead?: boolean;
}

/**
 * Notification Filters DTO
 */
export interface NotificationFiltersDto {
  search?: string;
  type?: NotificationTypeDto;
  priority?: NotificationPriorityDto;
  targetType?: NotificationTargetDto;
  isRead?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

/**
 * Bulk Notification Action DTO
 */
export interface BulkNotificationActionDto {
  action: "mark_read" | "mark_unread" | "delete";
  notificationIds: string[];
}

/**
 * Notification Preferences DTO
 */
export interface NotificationPreferencesDto {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  categories: {
    orders: boolean;
    products: boolean;
    users: boolean;
    system: boolean;
  };
}

/**
 * Validation Schemas
 */
export const CreateNotificationSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  message: z.string().min(1, "Message is required").max(1000, "Message too long"),
  type: z.nativeEnum(NotificationTypeDto).default(NotificationTypeDto.INFO),
  priority: z.nativeEnum(NotificationPriorityDto).default(NotificationPriorityDto.NORMAL),
  targetType: z.nativeEnum(NotificationTargetDto).default(NotificationTargetDto.ALL_ADMINS),
  targetId: z.string().uuid().optional(),
  actionUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
  expiresAt: z.string().datetime().optional(),
});

export const UpdateNotificationSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long").optional(),
  message: z.string().min(1, "Message is required").max(1000, "Message too long").optional(),
  type: z.nativeEnum(NotificationTypeDto).optional(),
  priority: z.nativeEnum(NotificationPriorityDto).optional(),
  targetType: z.nativeEnum(NotificationTargetDto).optional(),
  targetId: z.string().uuid().optional(),
  actionUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
  expiresAt: z.string().datetime().optional(),
  isRead: z.boolean().optional(),
});

export const NotificationFiltersSchema = z.object({
  search: z.string().optional(),
  type: z.nativeEnum(NotificationTypeDto).optional(),
  priority: z.nativeEnum(NotificationPriorityDto).optional(),
  targetType: z.nativeEnum(NotificationTargetDto).optional(),
  isRead: z.boolean().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
});

export const BulkNotificationActionSchema = z.object({
  action: z.enum(["mark_read", "mark_unread", "delete"]),
  notificationIds: z.array(z.string().uuid()).min(1, "At least one notification ID is required"),
});

export const NotificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  categories: z.object({
    orders: z.boolean(),
    products: z.boolean(),
    users: z.boolean(),
    system: z.boolean(),
  }),
});

/**
 * Type guards
 */
export const isValidNotificationType = (type: string): type is NotificationTypeDto => {
  return Object.values(NotificationTypeDto).includes(type as NotificationTypeDto);
};

export const isValidNotificationPriority = (priority: string): priority is NotificationPriorityDto => {
  return Object.values(NotificationPriorityDto).includes(priority as NotificationPriorityDto);
};

export const isValidNotificationTarget = (target: string): target is NotificationTargetDto => {
  return Object.values(NotificationTargetDto).includes(target as NotificationTargetDto);
};