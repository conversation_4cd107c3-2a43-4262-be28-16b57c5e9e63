/**
 * Common DTOs
 * Các DTOs chung được sử dụng trong toàn bộ ứng dụng
 */

import { z } from "zod";

/**
 * Base Response DTO
 */
export interface BaseResponseDto<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
  meta?: ResponseMetaDto;
}

/**
 * Response Meta DTO
 */
export interface ResponseMetaDto {
  timestamp: string;
  requestId?: string;
  version?: string;
  [key: string]: any;
}

/**
 * Pagination Request DTO
 */
export interface PaginationRequestDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * Pagination Response DTO
 */
export interface PaginationResponseDto<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Search Request DTO
 */
export interface SearchRequestDto extends PaginationRequestDto {
  search?: string;
  filters?: Record<string, any>;
}

/**
 * ID Parameter DTO
 */
export interface IdParamDto {
  id: string;
}

/**
 * Slug Parameter DTO
 */
export interface SlugParamDto {
  slug: string;
}

/**
 * File Upload DTO
 */
export interface FileUploadDto {
  filename: string;
  mimetype: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

/**
 * Error Response DTO
 */
export interface ErrorResponseDto {
  success: false;
  error: string;
  message?: string;
  details?: any;
  code?: string;
  statusCode?: number;
}

/**
 * Validation Schemas
 */
export const PaginationRequestSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const SearchRequestSchema = PaginationRequestSchema.extend({
  search: z.string().optional(),
  filters: z.record(z.any()).optional(),
});

export const IdParamSchema = z.object({
  id: z.string().uuid("Invalid ID format"),
});

export const SlugParamSchema = z.object({
  slug: z.string().min(1, "Slug is required"),
});

/**
 * SEO Data Schema
 */
export const seoDataSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  ogImage: z.string().url().optional(),
  canonicalUrl: z.string().url().optional(),
});

export type SEODataDto = z.infer<typeof seoDataSchema>;

/**
 * Common validation functions
 */
export const validatePagination = (data: any): PaginationRequestDto => {
  return PaginationRequestSchema.parse(data);
};

export const validateSearch = (data: any): SearchRequestDto => {
  return SearchRequestSchema.parse(data);
};

export const validateId = (data: any): IdParamDto => {
  return IdParamSchema.parse(data);
};

export const validateSlug = (data: any): SlugParamDto => {
  return SlugParamSchema.parse(data);
};

/**
 * Response builders
 */
export const createSuccessResponse = <T>(
  data: T,
  message?: string,
  meta?: ResponseMetaDto
): BaseResponseDto<T> => ({
  success: true,
  data,
  message,
  meta: {
    timestamp: new Date().toISOString(),
    ...meta,
  },
});

export const createErrorResponse = (
  error: string,
  message?: string,
  details?: any,
  code?: string,
  statusCode?: number
): ErrorResponseDto => ({
  success: false,
  error,
  message,
  details,
  code,
  statusCode,
});

export const createPaginationResponse = <T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  },
  meta?: ResponseMetaDto
): BaseResponseDto<PaginationResponseDto<T>> => ({
  success: true,
  data: {
    data,
    pagination,
  },
  meta: {
    timestamp: new Date().toISOString(),
    ...meta,
  },
});

/**
 * Status enums for DTOs
 */
export enum StatusDto {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  DRAFT = "DRAFT",
  ARCHIVED = "ARCHIVED",
}

export enum OrderStatusDto {
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  PROCESSING = "PROCESSING",
  SHIPPED = "SHIPPED",
  DELIVERED = "DELIVERED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
}

export enum PaymentStatusDto {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
}

export enum PaymentMethodDto {
  COD = "COD",
  BANK_TRANSFER = "BANK_TRANSFER",
  CREDIT_CARD = "CREDIT_CARD",
  DEBIT_CARD = "DEBIT_CARD",
  PAYPAL = "PAYPAL",
  DIGITAL_WALLET = "DIGITAL_WALLET",
}

export enum PaymentProviderDto {
  VNPAY = "VNPAY",
  MOMO = "MOMO",
  ZALOPAY = "ZALOPAY",
  PAYPAL = "PAYPAL",
  STRIPE = "STRIPE",
  BANK_TRANSFER = "BANK_TRANSFER",
  COD = "COD",
  CUSTOM = "CUSTOM",
}

export enum PaymentTransactionStatusDto {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
  PARTIAL_REFUND = "PARTIAL_REFUND",
}

export enum UserRoleDto {
  USER = "USER",
  ADMIN = "ADMIN",
  SUPER_ADMIN = "SUPER_ADMIN",
}

export enum AdminRoleDto {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN = "ADMIN",
  MODERATOR = "MODERATOR",
}

export enum GenderDto {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

export enum ProductStatusDto {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  OUT_OF_STOCK = "OUT_OF_STOCK",
  DISCONTINUED = "DISCONTINUED",
}

/**
 * Common validation schemas
 */
export const EmailSchema = z.string().email("Invalid email format");
export const PasswordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters");
export const PhoneSchema = z
  .string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/, "Invalid phone format");
export const UrlSchema = z.string().url("Invalid URL format");
export const SlugSchema = z
  .string()
  .regex(/^[a-z0-9-]+$/, "Invalid slug format");

/**
 * Date range DTO
 */
export interface DateRangeDto {
  from?: string; // ISO date string
  to?: string; // ISO date string
}

export const DateRangeSchema = z.object({
  from: z.string().datetime().optional(),
  to: z.string().datetime().optional(),
});

/**
 * Bulk operation DTOs
 */
export interface BulkOperationRequestDto {
  ids: string[];
  action: string;
  data?: Record<string, any>;
}

export interface BulkOperationResponseDto {
  success: number;
  failed: number;
  errors?: Array<{
    id: string;
    error: string;
  }>;
}

export const BulkOperationRequestSchema = z.object({
  ids: z.array(z.string().uuid()).min(1, "At least one ID is required"),
  action: z.string().min(1, "Action is required"),
  data: z.record(z.any()).optional(),
});

/**
 * Analytics DTOs
 */
export interface AnalyticsRequestDto {
  period: "day" | "week" | "month" | "year";
  from?: string;
  to?: string;
  groupBy?: string;
}

export interface AnalyticsDataPointDto {
  label: string;
  value: number;
  date?: string;
  metadata?: Record<string, any>;
}

export interface AnalyticsResponseDto {
  data: AnalyticsDataPointDto[];
  summary: {
    total: number;
    average: number;
    growth?: number;
    period: string;
  };
}

export const AnalyticsRequestSchema = z.object({
  period: z.enum(["day", "week", "month", "year"]),
  from: z.string().datetime().optional(),
  to: z.string().datetime().optional(),
  groupBy: z.string().optional(),
});
