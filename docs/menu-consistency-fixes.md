# Menu Module Consistency Fixes

## Tóm tắt

Đã kiểm tra và sửa tính nhất quán của menu module từ schema, models, dto, repositories, services (backend) đến contexts/hooks/components (frontend).

## Các vấn đề đã phát hiện và sửa

### 1. Schema vs Models Mismatch

**Vấn đề:**
- Schema có `Menu.location` là String, nhưng model có `MenuLocation` enum
- Schema có `MenuItem.order` nhưng model có `sortOrder`
- Schema có `MenuItem.type` với `MenuItemType` enum, nhưng model không có
- Schema có `MenuItem.target` là String?, nhưng model có `MenuTarget` enum

**Đã sửa:**
- Cập nhật `MenuEntity` để phù hợp với Prisma schema
- Thay thế `MenuLocation` và `MenuTarget` enum bằng `MenuItemType` enum từ schema
- Đổi `sortOrder` thành `order` để match với schema
- Cập nhật các interface `CreateMenuData`, `UpdateMenuData`, `CreateMenuItemData`, `UpdateMenuItemData`

### 2. Missing DTO

**Vấn đề:**
- Không có `menu.dto.ts` trong hệ thống DTO
- Không có transform functions cho menu

**Đã sửa:**
- Tạo `src/app/dto/menu.dto.ts` với đầy đủ DTOs:
  - `MenuDTO`, `MenuItemDTO`
  - `MenuWithItemsDTO`, `MenuItemWithChildrenDTO`
  - `CreateMenuDTO`, `UpdateMenuDTO`
  - `CreateMenuItemDTO`, `UpdateMenuItemDTO`
  - `MenuSearchFiltersDTO`, `MenuStatsDTO`
  - Zod validation schemas
- Tạo `src/app/dto/transforms/menu.transform.ts` với các transform functions
- Cập nhật `src/app/dto/index.ts` và `src/app/dto/transforms/index.ts`

### 3. Repository Layer Issues

**Vấn đề:**
- Repository sử dụng Prisma types trực tiếp thay vì DTOs
- Không có type consistency
- Methods trả về Prisma models thay vì DTOs

**Đã sửa:**
- Cập nhật `MenuRepository` để sử dụng DTOs:
  - `createMenu()` nhận `CreateMenuDTO` và trả về `MenuDTO`
  - `updateMenu()` nhận `UpdateMenuDTO` và trả về `MenuDTO`
  - `findByLocation()` trả về `MenuDTO | null`
  - `findByIdWithItems()` trả về `MenuWithItemsDTO | null`
  - `searchMenus()` nhận `MenuSearchFiltersDTO`
  - Tất cả menu item methods sử dụng DTOs
- Sử dụng transform functions để chuyển đổi giữa Prisma models và DTOs

### 4. Service Layer Issues

**Vấn đề:**
- Service sử dụng mix của model entities và repository responses
- Không có consistent data flow
- Type mismatches

**Đã sửa:**
- Cập nhật `MenuService` để sử dụng DTOs:
  - `createMenu()` nhận `CreateMenuDTO` và trả về `MenuDTO`
  - `updateMenu()` nhận `UpdateMenuDTO` và trả về `MenuDTO`
  - `getMenus()` nhận `MenuSearchFiltersDTO` và trả về `PaginatedResult<MenuDTO>`
  - `getActiveMenus()` trả về `MenuDTO[]`
- Loại bỏ manual type casting
- Sử dụng repository methods đã được cập nhật

### 5. Frontend Issues

**Vấn đề:**
- Hook `useAdminMenus` sử dụng `name` cho MenuItem nhưng schema có `title`
- Hook sử dụng `order` nhưng model sử dụng `sortOrder`
- Missing fields như `type`, `target`, `cssClass`
- API endpoints không đúng

**Đã sửa:**
- Cập nhật `useAdminMenus` hook:
  - Đổi `name` thành `title`
  - Thêm các fields: `type`, `target`, `icon`, `cssClass`
  - Cập nhật API endpoint từ `/api/admin/menu-items?menuId=${menuId}` thành `/api/admin/menus/${menuId}/items`
- Cập nhật interfaces `MenuItem` và `MenuItemFormData`

## Files đã được tạo/sửa

### Tạo mới:
1. `src/app/dto/menu.dto.ts` - Menu DTOs và validation schemas
2. `src/app/dto/transforms/menu.transform.ts` - Transform functions

### Cập nhật:
1. `src/app/dto/index.ts` - Export menu DTOs
2. `src/app/dto/transforms/index.ts` - Export menu transforms
3. `src/app/models/menu.model.ts` - Cập nhật entities để match schema
4. `src/app/api/repositories/menu.repository.ts` - Sử dụng DTOs và transforms
5. `src/app/api/services/menu.service.ts` - Sử dụng DTOs
6. `src/hooks/admin/useAdminMenus.ts` - Cập nhật interfaces và API calls

## Lợi ích đạt được

### 1. Type Safety
- Đảm bảo type consistency từ database đến frontend
- Loại bỏ type casting không an toàn
- Validation tự động với Zod schemas

### 2. Maintainability
- Clear separation of concerns với DTOs
- Transform functions tái sử dụng được
- Consistent data flow

### 3. Developer Experience
- IntelliSense tốt hơn với proper types
- Compile-time error detection
- Self-documenting code với DTOs

### 4. Data Integrity
- Validation ở multiple layers
- Consistent field names và types
- Proper null/undefined handling

## Các lỗi TypeScript còn lại

Sau khi chạy `npx tsc`, vẫn còn một số lỗi TypeScript nhưng **KHÔNG liên quan đến menu module**:

1. **Menu-related errors đã được fix:**
   - `MenuLocation` import errors → Fixed
   - Type mismatches → Fixed
   - Missing DTOs → Fixed

2. **Các lỗi còn lại thuộc về modules khác:**
   - Contact module
   - Order module  
   - Post module
   - User module
   - Admin pages
   - Promotion module

## Kết luận

Menu module đã được sửa hoàn toàn để đảm bảo tính nhất quán từ schema đến frontend. Tất cả các vấn đề về type safety, data flow, và API consistency đã được giải quyết.

Các lỗi TypeScript còn lại không liên quan đến menu module và cần được xử lý riêng cho từng module tương ứng.